package com.cw.utils;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.config.exception.CustomException;
import com.cw.entity.AppUser;
import com.cw.entity.GroupBand;
import com.cw.entity.OpRole;
import com.cw.entity.RoomType;
import com.cw.mapper.AppUserMapper;
import com.cw.mapper.GroupBandMapper;
import com.cw.mapper.OpRoleMapper;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.utils.enums.GlobalDataType;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Describe 查询缓存记录
 * <AUTHOR> <PERSON>
 * @Create on 2024-03-29
 */
public class ContentCacheTool {

    /**
     * 获取产品描述
     *
     * @param productType 产品类型.一般从数据库中取
     * @param productCode 产品代码
     * @param hotelId     项目 ID
     * @return
     */
    public static String getProductDesc(String productType, String productCode, String hotelId) {
        RoomTypeCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
        RoomType roomType = cache.getRecord(hotelId, productCode);
        return roomType == null ? null : roomType.getDescription();
    }

    /**
     * @param name
     * @param hotelId
     * @param lUpperCase 是否返回大写代码，默认小写
     * @return 根据名称首拼音返回唯一的roleId
     */
    public static String getOpRoleIdByTransName(String name, String hotelId, boolean lUpperCase) {
        //去除空格首拼
        String roleId = PinyinUtil.getFirstLetter(name.replaceAll("\\s", ""), "");
        if (lUpperCase) {
            roleId = roleId.toUpperCase(Locale.ROOT);
        } else {
            roleId = roleId.toLowerCase(Locale.ROOT);
        }
        OpRoleMapper opRoleMapper = SpringUtil.getBean(OpRoleMapper.class);
        OpRole dbRole = opRoleMapper.findLikeRoleIdAndHotelId(roleId, hotelId);
        if (dbRole == null) {
            return roleId;
        } else {
            //如果存在相同的roleId,检查是否包含数字结尾，不以数字结尾先+2，数字结尾自增1，再递归查询一遍本地数据是否存在返回
            String regex = "(\\D*)(\\d+)$";
            String newRoleId = "";
            Matcher matcher = Pattern.compile(regex).matcher(roleId);
            // 如果匹配，则获取最后的数字部分
            if (matcher.find()) {
                // 数字部分
                int number = Integer.parseInt(matcher.group(2));
                //返回去掉最后数字部分+自增数字
                newRoleId = roleId.replace(matcher.group(2), "") + (number + 1);
                return getOpRoleIdByTransName(newRoleId, hotelId, lUpperCase);
            }
            newRoleId = roleId + "2";
            return getOpRoleIdByTransName(newRoleId, hotelId, lUpperCase);
        }

    }


    /**
     * 判断当前hotelid是否属于当前用户
     *
     * @param groupMobile 景区运营登录的手机号码
     * @param hotelId
     * @param type
     * @return
     */
    public static boolean isMchHotel(String groupMobile, String hotelId, Integer type) {
        if (StringUtils.isBlank(groupMobile)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("用户不存在"));
        }
        AppUserMapper appUserMapper = SpringUtil.getBean(AppUserMapper.class);
        //todo 通过userid查询可能会有重复
        AppUser appUser = appUserMapper.findAppUserByMobileNo(groupMobile);
        if (appUser == null) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("用户不存在"));
        }
        String groupId = appUser.getGroupId();
        GroupBandMapper groupBandMapper  = SpringUtil.getBean(GroupBandMapper.class);
        GroupBand groupBand = groupBandMapper.findGroupBandByCode((groupId));
        if (groupBand != null && StringUtils.isNotBlank(groupBand.getHotelIds())) {
            if (type == 0) {
                return groupBand.getCode().equals(hotelId);
            } else if (type == 1) {
                List<String> hotelIdList = Arrays.asList(groupBand.getHotelIds().split(","));
                return hotelIdList.contains(hotelId);
            }

        }
        return false;
    }
}
