package com.cw.utils;

import cn.hutool.core.date.DateUtil;
import com.cw.config.exception.CustomException;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class SystemUtil {

    public static final int DEFAULT_PROFILE_REMOVE_DAY = 30; //没有办理入住的档案删除配置天数
    public static String hotelIdColumn = "hotelId";
    public static final String RESERVE_ROLE = "supervisor";
    public static final String DEFAULT_SPLIT_CHAR = "_";
    public static final String SYSHOTELID = "SYS";
    public static final String SYSUSERID = "SYS";

    public static final String CONSOLEHOTELID = "CONSOLE"; //标示这个数据属于运营端
    public static final String NASUCCESS = "成功"; //标示夜审步骤状态成

    public static final String DEF_DEPTCODE = "1000";  //默认入账账项代码

    public static final String CASH_DEPTCODE = "9000";  //9000以下的是消费.用来定义分界线

    public static final String DEF_PADUSER = "PAD";  //自助机登陆用户
    public static final Integer DEFAULT_NA_MOVE_DAY = 45; //夜审迁移订单指定提前天数时间
    public static final Integer DEFAULT_NA_DELETE_RESOURCE_DAY = 45; //夜审迁移订单指定提前天数时间
    public static final Integer DEFAULT_NA_RESOURCE_DAY = 30; //夜审产品资源自动延长天数

    public static final String DEF_RESOURCEID = "FS";  //默认库存来源渠道 直接扣减酒店的库存

    public static final String PARAMSIGNAL = "|";

    public static final Date EMPTY_DATETIME = DateUtil.parse("1900-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
    public static final LocalDateTime EMPTY_LOCALTIME = LocalDateTime.of(1900, 1, 1, 0, 0, 0);


    public enum SequenceKey {//订单序列号类型
        ORDERID, PREPAY
    }

    public enum FactoryType {//自定义数据类型
        PROVINCE("省份"), COUNTRY("国家"), CITY("城市"),
        SOURCEONE("一级资源"), SOURCETWO("二级资源"),
        ;
        //原因
        private String desc;

        FactoryType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }


    /**
     * 资源上级分类类型
     */
    public enum SourceType {
        PROVINCE("省份"), COUNTRY("国家"), CITY("城市"),
        ;
        private String desc;

        SourceType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    public static String transFactoryTypeName(String type) {
        SystemUtil.FactoryType[] values = SystemUtil.FactoryType.values();
        String typeName = null;
        for (SystemUtil.FactoryType factoryType : values) {
            if (factoryType.name().equalsIgnoreCase(type)) {
                typeName = factoryType.name();
            }
        }
        if (typeName == null) {
            throw new CustomException(ResultJson.failure(ResultCode.BAD_REQUEST).msg("查询类型不存在"));
        }
        return typeName;

    }

    /**
     * 存放到全局缓存中的数据类型
     */
    public enum GlobalDataType {
        ALL("所有"),  //特殊标记.刷新所有数据用
        RATECODE("房价"), OPTIONSWITCH("参数开关"), SYSCONF("民宿基本信息"), CHANNEL("渠道"), NA_PARAM("夜审参数"), FACTOR("基础配置"),
        //用户
        USER_ROLE("用户角色"), USER("用户"), OP_ROLE_RIGHT("角色权限"),
        ;

        private String desc = "";

        GlobalDataType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 前后端交互的选择类数据
     */
    public enum CustomDataKey {
        channel("渠道"), hotel("酒店"), roomtype("房型"), roomrate("房价"), payment("付款方式"), vendortype("厂商类型"), factor("基础配置"),
        userrole("用户角色"), ratecode("房价代码"), room("房间"), opuser("操作用户"),
        category("资源上级分类"), sourceone("一级资源"), sourcetwo("二级资源"), province("省份"), country("国家"), city("城市"),
        building("楼栋"), includerate("包价"), accountitem("账项代码"), incometype("收入组"), characteristic("房间特性"), restype("预定类型"),
        market("市场"), userlogtype("操作日志类型"), bedtype("床型"), araccount("AR应收账户"),
        saler("销售人员"), floor("楼层"), doorlockbrand("门锁配置"), roomstaff("房务人员类型"), roomstaffshift("排班表状态")
        ;

        private String desc = "";

        CustomDataKey(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        public static CustomDataKey getCustomDataKey(String name) {
            SystemUtil.CustomDataKey[] values = SystemUtil.CustomDataKey.values();
            for (SystemUtil.CustomDataKey customDataKey : values) {
                if (customDataKey.name().equalsIgnoreCase(name)) {
                    return customDataKey;
                }
            }
            return CustomDataKey.userlogtype;

        }
    }

    public class ScheduleOp {
        //更新普通间隔线程
        public final static int SIMPLE_TRIGGER = 0;
        //更新指定时间的线程
        public final static int CRON_TRIGGER = 1;
        //马上触发一次定时任务
        public final static int TRIGGER_NOW = 2;
        //删除定时线程任务
        public final static int DELETE_TRIGGER = 999;

    }


    /**
     * 用户日志类型枚举
     */
    public enum UserLogType {
        HOTEL("酒店"),
        ROOM_TYPE("房型"),
        ROOM_RATE("房价"),
        INCLUDE_RATE("包价"),
        COL("订单"),
        OP_ROLE("角色"),
        OP_USER("用户"),
        INVOICE("发票"),
        FACTOR("基础配置"),

        NA("夜审设置"),

        ;


        private String desc;

        UserLogType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 用户操作类型
     */
    public enum UserLogOpType {
        NEW,//新建
        MODIFY,//编辑
        CANCEL,//取消
        DELETE,//删除
        SYNC,//同步
        PAY,//付款
        REFUND,//退款
    }

    /**
     * 房务人员岗位类型
     */
    public enum PositionType {
        CL("保洁员"),//保洁员
        INSP("检查员");//检查员


        private String positionDesc;

        PositionType(String positionDesc) {
            this.positionDesc = positionDesc;
        }

        public String getPositionDesc() {
            return positionDesc;
        }

        public void setPositionDesc(String positionDesc) {
            this.positionDesc = positionDesc;
        }
    }

    /**
     * 工作人员排班表状态
     */
    public enum StaffShiftStatus {
        CLOSE("关闭"),//保洁员
        ACTIVE("活跃");//检查员


        private String statusDesc;

        StaffShiftStatus(String statusDesc) {
            this.statusDesc = statusDesc;
        }

        public String getStatusDesc() {
            return statusDesc;
        }

        public void setStatusDesc(String statusDesc) {
            this.statusDesc = statusDesc;
        }

        public StaffShiftStatus toggle() {
            return this == CLOSE ? ACTIVE : CLOSE;
        }
    }
}
