package com.cw.utils.jpa;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * @Classname DynamicSpecificationBuilder
 * @Description 动态构建Specification
 * @Date 2024-03-26 20:52
 * <AUTHOR> sancho.shen
 */
public class DynamicSpecificationBuilder {

    /**
     * 根据给定的实体类和搜索条件列表生成查询规范
     *
     * @param entityClass        实体类
     * @param searchCriteriaList 搜索条件列表
     * @param <T>                泛型类型
     * @return 查询规范
     */
    public static <T> Specification<T> getQuerySpecification(Class<T> entityClass, List<SearchCriteria> searchCriteriaList) {
        return (root, query, criteriaBuilder) -> {
            // 创建一个CriteriaQuery对象，指定查询的返回类型为实体类类型
            CriteriaQuery<T> cq = criteriaBuilder.createQuery(entityClass);
            // 初始条件设置为true，用于后续添加条件
            Predicate predicate = criteriaBuilder.conjunction();

            // 遍历搜索条件列表
            for (SearchCriteria searchCriteria : searchCriteriaList) {
                // 根据搜索条件创建Predicate对象
                Predicate criterion = createPredicate(root, searchCriteria, criteriaBuilder);
                // 如果创建的Predicate对象不为空
                if (criterion != null) {
                    // 将当前Predicate对象与之前的Predicate对象进行逻辑与操作，并将结果赋值给predicate
                    predicate = criteriaBuilder.and(predicate, criterion);
                }
            }

            // 设置CriteriaQuery的查询条件和查询结果
            cq.select(root).where(predicate);
            // 返回CriteriaQuery的查询条件限制
            return cq.getRestriction();
        };
    }


    /**
     * 根据给定的实体类字段、搜索条件和CriteriaBuilder，生成Predicate对象。
     *
     * @param root            实体类的根对象
     * @param searchCriteria  搜索条件
     * @param criteriaBuilder JPA的CriteriaBuilder对象
     * @param <T>             泛型类型
     * @return 返回Predicate对象，用于构建JPA查询条件
     */
    private static <T> Predicate createPredicate(Root<T> root, SearchCriteria searchCriteria, CriteriaBuilder criteriaBuilder) {
        // 获取搜索条件中的字段名
        String fieldName = searchCriteria.getFieldName();
        // 获取搜索条件中的值
        Object value = searchCriteria.getValue();
        // 如果值为空，则直接返回null
//        if (ObjectUtil.isEmpty(value)) {
//            return null;
//        }
        // 获取搜索条件中的操作符
        Operator operator = searchCriteria.getOperator();

        // 合并null和空字符串的判断逻辑
//        if (searchCriteria.getValue() == null ||
//                (searchCriteria.getValue() instanceof String && StrUtil.isBlank((String) searchCriteria.getValue()))) {
//
//            if (searchCriteria.getOperator() == Operator.NOT_EQUAL) {
//                // 统一处理null和空字符串的NOT_EQUAL情况
//                return criteriaBuilder.and(
//                        criteriaBuilder.isNotNull(root.get(searchCriteria.getFieldName())),
//                        criteriaBuilder.notEqual(root.get(searchCriteria.getFieldName()), "")
//                );
//            }
//            return null;
//        }

        // 根据操作符的不同，生成对应的Predicate对象
        switch (operator) {
            case EQUAL:
                if (value instanceof String && StrUtil.isBlank((String) value)) {
                    return null;
                }

                if (value == null) {
                    return null;
                }
                // 等于操作符，生成等于的Predicate对象
                return criteriaBuilder.equal(root.get(fieldName), value);
            case LIKE:
                // LIKE操作符，生成模糊匹配的Predicate对象
                if (value instanceof String && StrUtil.isNotBlank((String) value)) {
                    return criteriaBuilder.like(root.get(fieldName), "%" + value + "%");
                }
                break;
            case GREATER_THAN:
                // 大于操作符，生成大于的Predicate对象
                if (value instanceof Comparable) {
                    return criteriaBuilder.greaterThan(root.get(fieldName), (Comparable) value);
                }
                break;
            case GREATER_THAN_EQUAL:
                // 大于等于操作符，生成大于等于的Predicate对象
                if (value instanceof Comparable) {
                    return criteriaBuilder.greaterThanOrEqualTo(root.get(fieldName), (Comparable) value);
                }
                break;
            case LESS_THAN:
                // 小于操作符，生成小于的Predicate对象
                if (value instanceof Comparable) {
                    return criteriaBuilder.lessThan(root.get(fieldName), (Comparable) value);
                }
                break;
            case LESS_THAN_EQUAL:
                // 小于等于操作符，生成小于等于的Predicate对象
                if (value instanceof Comparable) {
                    return criteriaBuilder.lessThanOrEqualTo(root.get(fieldName), (Comparable) value);
                }
                break;
            case BETWEEN:
                // BETWEEN操作符，生成范围匹配的Predicate对象
                if (value instanceof List && ((List<?>) value).size() == 2 && ((List<?>) value).get(0) instanceof Comparable && ((List<?>) value).get(1) instanceof Comparable) {
                    List<?> range = (List<?>) value;
                    return criteriaBuilder.between(root.get(fieldName), (Comparable) range.get(0), (Comparable) range.get(1));
                }
                break;
            case IN:
                // IN操作符，生成在集合中的Predicate对象
//                if (value instanceof Collection) {
//                    Collection<?> collection = (Collection<?>) value;
//                    return root.get(fieldName).in(collection);
//                }
                if (value instanceof Collection && !CollectionUtil.isEmpty((Collection<?>) value)) {
                    Collection<?> collection = (Collection<?>) value;
                    return root.get(fieldName).in(collection);
                }
                break;
            // 其他操作符...
            case NOT_EQUAL:
                 if (value instanceof String && StrUtil.isBlank((String) value)) {
                     return criteriaBuilder.and(
                             criteriaBuilder.isNotNull(root.get(fieldName)),
                             criteriaBuilder.notEqual(root.get(fieldName), "")
                     );
                }
                return criteriaBuilder.notEqual(root.get(fieldName), value);
            case NOT_IN:
                // NOT_IN操作符，生成除了这个集合范围内的Predicate对象
                if (value instanceof Collection) {
                    Collection<?> collection = (Collection<?>) value;
                    return criteriaBuilder.not(root.get(fieldName).in(collection));
                }
                // NOT_IN操作符，生成除了这个集合范围内的Predicate对象
                if (value instanceof Collection && !CollectionUtil.isEmpty((Collection<?>) value)) {
                    Collection<?> collection = (Collection<?>) value;
                    return criteriaBuilder.not(root.get(fieldName).in(collection));
                }
                break;
        }
        // 如果无法匹配到任何操作符，则返回null
        return null;
    }


    /**
     * 根据给定的实体类、搜索条件列表、Root、CriteriaQuery 和 CriteriaBuilder 生成 Predicate 列表
     *
     * @param entityClass        实体类
     * @param searchCriteriaList 搜索条件列表
     * @param root               实体类的根对象
     * @param query              JPA 的 CriteriaQuery 对象
     * @param cb                 JPA 的 CriteriaBuilder 对象
     * @param <T>                泛型类型
     * @return Predicate 列表
     */
    public static <T> List<Predicate> getPredicates(Class<T> entityClass, List<SearchCriteria> searchCriteriaList, Root<T> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        List<Predicate> predicates = CollectionUtil.newArrayList();
        for (SearchCriteria searchCriteria : searchCriteriaList) {
            Predicate predicate = createPredicate(root, searchCriteria, cb);
            if (predicate != null) {
                predicates.add(predicate);
            }
        }
        return predicates;
    }
}