package com.cw.utils.tool;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.function.Function;

public class DefValData<K, V> {
	Map<K, V> dataMap;

	Function<K, V> initHandler;

	public DefValData() {
		this(new LinkedHashMap<K, V>());
	}

	public DefValData(Map<K, V> dataMap) {
		this.dataMap = dataMap;
	}

	public DefValData<K, V> setInitHandler(Function<K, V> initHandler) {
		this.initHandler = initHandler;
		return this;
	}

	public Map<K, V> getDataMap() {
		return dataMap;
	}

	public DefValData<K, V> put(K k, V v) {
		getDataMap().put(k, v);
		return this;
	}

	public V get(K k) {
		if (!getDataMap().containsKey(k) && null != initHandler) {
			put(k, initHandler.apply(k));
		}
		return getDataMap().get(k);
	}

}
