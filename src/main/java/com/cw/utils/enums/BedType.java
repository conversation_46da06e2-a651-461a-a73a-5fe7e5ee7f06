package com.cw.utils.enums;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/5 16:37
 **/
public enum BedType {

    BedType_1("1", "大床"),
    BedType_2("2", "双床"),
    BedType_3("3", "单人床"),
    BedType_4("4", "特大床"),
    BedType_5("5", "榻榻米"),
    BedType_6("6", "儿童床"),
    BedType_7("7", "上下铺"),
    BedType_8("8", "沙发床"),
    BedType_9("9", "标间"),
    BedType_10("10", "套房"),
    BedType_11("11", "家庭房"),
    BedType_12("12", "床位"),
    BedType_13("13", "行政房");

    private String val;
    private String desc;

    private BedType(String val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static String getBedDesc(String val) {
        try {
            BedType type = valueOf("BedType_" + val);
            return type.getDesc();
        } catch (IllegalArgumentException var2) {
            return "大床";
        }
    }

    public String getVal() {
        return this.val;
    }

    public String getDesc() {
        return this.desc;
    }
}
