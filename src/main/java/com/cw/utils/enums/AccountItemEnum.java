package com.cw.utils.enums;

import java.util.Collection;

import cn.hutool.core.collection.CollectionUtil;

/*
 * 定义保留帐项代码
 *
 * @Describe
 * <AUTHOR> Just
 * @Create on 2024-06-27
 */
public enum AccountItemEnum {
    ROOM_CHARGE("1000", "房费"),
    CASH("9000","现金"),
    WXQR("9100", "微信当面付"),  //扫码器服务商支付
    ALIQR("9110", "支付宝当面付"),//扫码器服务商支付
    AR("9600", "应收支付"), //系统保留的默认应收支付账项代码.选择9300.可以任选挂账账户
    ARDEBTS("8100", "应收待核销"),
    BADDEBTS("8101", "坏账"),
    ROUNDING("8102", "折扣"),
    ARBLCTRSF("9550", "应收余额转结")
    ;

    private String code;
    private String description;

    AccountItemEnum(String code,String description) {
        this.code= code;
        this.description = description;
    }


    public static boolean isSysOnlinePay(String code) {
        if (code != null && (code.equals(WXQR.code) || code.equals(ALIQR.code))) {
            return true;
        }
        return false;
    }

    public static boolean isArPay(String code) {
        return code != null && code.startsWith("96");//暂定96开头的为应收账项
    }
    
	/**
	 * 系统自用的应收帐操作码
	 */
	public static Collection<String> SYSTEM_AR_CODES = CollectionUtil.toList(ARDEBTS.getCode(), ARBLCTRSF.getCode());

    /**
     *
     * @param code
     * @return 判断代码是否基础配置账项代码
     */
    public static boolean isAccountItemEnum(String code) {
        AccountItemEnum[] values = AccountItemEnum.values();
        for (AccountItemEnum node : values) {
            if (node.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
