package com.cw.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.concurrent.TimeUnit;


public class JwtUtils {

    public static String token_header = "Authorization";
    public static final String LOGIN_HOTELID_KEY = "loginhotel";
    public static final String LOGIN_BUILDING_KEY = "building";
    public static final String JwtPrefix = "Bearer ";
    private final SignatureAlgorithm SIGNATURE_ALGORITHM = SignatureAlgorithm.HS512;
    private Long access_token_expiration = TimeUnit.MINUTES.toMillis(5);
    private Long refresh_token_expiration = TimeUnit.MINUTES.toMillis(5);
    public static final String LOGIN_USERID_KEY = "loginId";
    public static final String LOGIN_USERNAME_KEY = "loginUserName";
    public static int jwtExpireMin = 80 * 10; // 目前是自动续
    public static final String LOGIN_SIDE_KEY = "loginside";
    public static final String LOGIN_AGENT_KEY = "loginagent";
    public static final String LOGIN_GROUP_MOBILE_KEY = "loginGroupMobile";
    public static final String LOGIN_OUTOPENID = "outopenid";// 微信小程序,公众号,支付宝之类的外部 openid
    private static String secret = "oqfu7ffe6sahowzpw5zpvrnbqk296278rqwr0infkfssaxsulg5r"; //TODO  后期改成从配置文件加载 暂时都用一个公共密钥签名.后面改成每个用户自己一个
    private static String splitSignal = "@@@@@";
    private static SecretKey key = Keys.hmacShaKeyFor(Decoders.BASE64.decode(secret));


    public static String getUserIdFromSaLoginId(Object loginId) {
        return ArrayUtil.get(String.valueOf(loginId).split(splitSignal), 0);
    }

    public static String getHotelIdFromSaLoginId(Object loginId) {
        return ArrayUtil.get(String.valueOf(loginId).split(splitSignal), 2);
    }

    public static String getUserIdFromToken(String token) {
        token = token.replace(JwtPrefix, "");
        Claims claims = getClaimsFromToken(token);
        return claims.getSubject();
    }


    public static Claims getClaimsFromToken(String token) {
        Claims claims;
        try {
            //claims = Jwts.parser()
            //        .setSigningKey(secret)
            //        .build()
            //        .parseClaimsJws(token.replace(JwtPrefix, ""))
            //        .getBody();
            claims = (Claims) Jwts.parser().verifyWith(key).build().parse(token).getPayload();
        } catch (ExpiredJwtException e) {
            claims = e.getClaims();
        }
        return claims;
    }


    public static String generateToken(LoginJwtForm form) {
        Date signdate = new Date();
        JwtBuilder builder = Jwts.builder();
        builder.setHeaderParam("type", "JWT")
                .claim(LOGIN_USERID_KEY, form.getUserId())
                .claim(LOGIN_SIDE_KEY, form.getEntranceType().name())
                .claim(LOGIN_HOTELID_KEY, form.getHotelId())
                .claim(LOGIN_AGENT_KEY, form.getAgentType())
                .claim(LOGIN_GROUP_MOBILE_KEY, form.getGroupMobile())
                .claim(LOGIN_BUILDING_KEY, form.getBuildingNo())
                .claim(LOGIN_USERNAME_KEY, form.getUserName())
                .setSubject(form.getUserId())
                .setIssuedAt(signdate)
                .setExpiration(DateUtil.offsetMinute(signdate, jwtExpireMin))
                .signWith(key);
        //.signWith(SignatureAlgorithm.HS512, secret);

        if (form.getOutOpenId() != null) {
            builder.claim(LOGIN_OUTOPENID, form.getOutOpenId());
        }
        return builder.compact();
    }
}
