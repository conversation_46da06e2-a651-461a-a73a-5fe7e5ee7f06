package com.cw.utils.rights;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019-07-02 10:01
 **/
public enum RightGroup {
    MENU_PC("PC端主菜单", RightModule.MENU),
    HMS_OD("订单管理", RightModule.HMS),
    HMS_RMT("房态管理", RightModule.HMS),
    HMS_ACC("账务管理", RightModule.HMS),

    RP_PANNEL("查看面板", RightModule.RP),
    RP_SUM("报表", RightModule.RP),



    CF_HOTELINFO("门店信息", RightModule.CONFIG),

    CF_ROOMT_SETTING("客房设置", RightModule.CONFIG),

    // CF_ROOMTYPE("房型设置", RightModule.CONFIG),
    // CF_ROOM("房间设置", RightModule.CONFIG),
    // CF_BUILDING("楼栋管理", RightModule.CONFIG),
    // CF_CHARACTERISTIC("房间特性", RightModule.CONFIG),
    // CF_FLOOR("楼层管理", RightModule.CONFIG),
    // CF_DOORLOCK("门锁管理", RightModule.CONFIG),


    CF_ROOM_RATE("房价管理", RightModule.CONFIG),

    // CF_ROOMRATE("房价设置", RightModule.CONFIG),
    // CF_BATCHPRICE("批量改价", RightModule.CONFIG),
    // CF_INCLUDERATE("包价设置", RightModule.CONFIG),


    CF_PAY_MANAGEMENT("财务管理", RightModule.CONFIG),
    // CF_ACCOUNTITEM("账项设置", RightModule.CONFIG),
    // CF_PAYMENT("支付方式", RightModule.CONFIG),
    // CF_ARACCOUNT("应收账户", RightModule.CONFIG),

    CF_BASE("基础设置", RightModule.CONFIG),
    // CF_HOTEL("酒店信息", RightModule.CONFIG),
    // CF_BAND("酒店品牌", RightModule.CONFIG),
    // CF_NA("夜审设置", RightModule.CONFIG),
    // CF_MARKET("市场管理", RightModule.CONFIG),
    // CF_RESTYPE("预定类型", RightModule.CONFIG),
    // CF_CHANNEL("渠道管理", RightModule.CONFIG),
    // CF_USER("用户管理", RightModule.CONFIG),
    // CF_ROLE("角色管理", RightModule.CONFIG),
    // CF_OPERATE("运维管理", RightModule.CONFIG),
    // CF_SALER("分销员管理", RightModule.CONFIG),


;

    private String desc;
    private RightModule rightModule;

    RightGroup(String desc, RightModule rightModule) {
        this.desc = desc;
        this.rightModule = rightModule;
    }

    public String getDesc() {
        return desc;
    }

    public static String getModuleDesc(RightGroup rightGroup) {
        return rightGroup.rightModule.getDesc();
    }

    public RightModule getRightModule() {
        return rightModule;
    }


}
