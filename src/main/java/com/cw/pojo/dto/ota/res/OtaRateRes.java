package com.cw.pojo.dto.ota.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * OTA房价响应对象
 *
 * <AUTHOR>
 * @date 2024/3/31
 */
@Data
public class OtaRateRes {

    /**
     * 房型代码
     */
    private String roomType;

    /**
     * 房价代码
     */
    private String rateCode;

    /**
     * 日期
     */
    private Date date;

    /**
     * 房价
     */
    private BigDecimal price;

    /**
     * 早餐数量
     */
    private Integer breakfast;
} 