package com.cw.pojo.dto.ota.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * OTA订单响应对象
 *
 * <AUTHOR>
 * @date 2024/3/31
 */
@Data
public class OtaOrderRes {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 渠道订单号
     */
    private String channelOrderNo;

    /**
     * 房型代码
     */
    private String roomType;

    /**
     * 房型名称
     */
    private String roomTypeName;

    /**
     * 入住日期
     */
    private Date checkInDate;

    /**
     * 离店日期
     */
    private Date checkOutDate;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 支付状态
     */
    private String payStatus;
} 