package com.cw.pojo.dto.mch.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2025/6/24 14:12
 */
@Data
@ApiModel(description = "商家集团运营用户关联酒店列表，第一个默认集团名称")
public class MchUserHotelInfo {
    @ApiModelProperty(value = "酒店代码/集团代码", example = "TEST", required = true)
    private String hotelId;
    @ApiModelProperty(value = "酒店名称/集团名", example = "测试酒店", required = true)
    private String hotelName;
    @ApiModelProperty(value = "类型，0-集团，1-酒店", example = "0", required = true)
    private Integer type;
}
