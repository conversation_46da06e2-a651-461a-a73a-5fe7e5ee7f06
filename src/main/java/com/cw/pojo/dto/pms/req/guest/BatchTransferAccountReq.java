package com.cw.pojo.dto.pms.req.guest;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/15 21:45
 * @Description 查询账户总额请求体
 **/
@Data
@ApiModel(description = "批量结账")
public class BatchTransferAccountReq {

    @Size(min = 2, message = "请至少选择两个订单")
    @ApiModelProperty(value = "原始预订号", example = "**********")
    private List<String> regNos = Lists.newArrayList();

    @ApiModelProperty(value = "转账备注")
    private String remark;
}
