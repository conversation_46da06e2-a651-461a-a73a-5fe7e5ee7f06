package com.cw.pojo.dto.pms.res.reservation;

import com.cw.entity.Colrs;
import com.cw.pojo.dto.common.res.PageResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

@Data
@ApiModel(description = "用户设置列表返回")
public class GroupRsListRes extends PageResponse<GroupRsListRes.GroupListData, Colrs> {

    @Data
    public class GroupListData {
        @ApiModelProperty(value = "唯一id", example = "1")
        Integer id = 0;
        @ApiModelProperty(value = "团队名称", example = "团队名称")
        private String groupname;

        @ApiModelProperty(value = "预订人姓名", example = "2")
        private String bookerName;


        @ApiModelProperty(value = "预订人电话", example = "13800138000")
        private String telephone = "";

        @ApiModelProperty(value = "到店日期yyyy-MM-dd ", example = "2024-03-27 ")
        @JsonFormat(pattern = "yyyy-MM-dd ")
        private Date arrivalDate;

        @ApiModelProperty(value = "离日期yyyy-MM-dd", example = "2024-03-28 ")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date departureDate;


        @Column(name = "bookingid", length = 20, nullable = false, columnDefinition = " varchar(20)  default '' comment '主单号' ")
        private String bookingid;  //客房订单中的

    }

}

