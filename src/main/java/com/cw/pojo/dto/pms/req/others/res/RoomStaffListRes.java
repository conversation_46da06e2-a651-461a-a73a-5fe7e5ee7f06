package com.cw.pojo.dto.pms.req.others.res;

import com.cw.entity.Floor;
import com.cw.entity.RoomStaff;
import com.cw.pojo.dto.common.res.PageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/5/13
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "房屋人员列表响应")
public class RoomStaffListRes extends PageResponse<RoomStaffListRes.RoomStaffListData, RoomStaff> {


    @Data
    @ApiModel(description = "房屋人员列表数据")
    public class RoomStaffListData {
        @ApiModelProperty(value = "对象唯一id", example = "1")
        Integer id = 0;

        @ApiModelProperty(value = "姓名")
        private String name;

        @ApiModelProperty(value = "企微员工id")
        private String wxUserId;

        @ApiModelProperty(value = "人员类型")
        private String positionType ;
    }
}
