package com.cw.pojo.dto.pms.req.reservation;

import com.cw.pojo.dto.pms.req.profile.ProfileReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Classname AssignRoomReq
 * @Description 分房入参
 * @Date 2024-06-06 22:11
 * <AUTHOR> sancho.shen
 */
@Data
@ApiModel(value = "分房入参")
public class AssignRoomReq implements Serializable {
    @ApiModelProperty(value = "预订编号,非团队预订时必填")
    private String reservationNumber;

    @ApiModelProperty(value = "团队编号,团队分房时必填")
    private String groupNumber;

    @ApiModelProperty(value = "分房信息集合", required = true)
    @Size(min = 1, message = "分房信息不能为空")
    private List<AssignRoomInfo> assignRoomInfoList;

}
