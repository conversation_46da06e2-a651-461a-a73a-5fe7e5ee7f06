package com.cw.pojo.dto.pms.req.guest;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 客人账目请求实体
 * @Author: michael.pan
 * @Date: 2024/3/27 15:58
 */
@Data
@ApiModel(description = "客人账目请求对象")
public class GuestAccountReq {

    /**
     * 预定号
     */
    @ApiModelProperty(value = "预定号", required = true, example = "**********")
    @NotBlank(message = "预定号不能为空")
    private String reservationNumber;

    /**
     * 账项代码
     */
    @ApiModelProperty(value = "账项代码", required = true, example = "ZX")
    private String departmentCode;

    /**
     * 入账描述
     */
    @ApiModelProperty(value = "账项描述")
    private String description;

    /**
     * 金额
     * @deprecated 不建议使用金额，使用单价配合数量
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 金额
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 金额
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 营业日期（帐应该累计到的日期），可空，空则取服务器日期
     */
    @ApiModelProperty(value = "营业日期")
    private Date business_date;
}
