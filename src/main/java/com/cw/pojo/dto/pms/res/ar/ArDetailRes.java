package com.cw.pojo.dto.pms.res.ar;

import java.io.Serializable;
import java.math.BigDecimal;

import com.cw.pojo.dto.pms.res.BaseRes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 应收帐响应
 * @Date: 2024/3/27 20:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "应收帐响应对象")
public class ArDetailRes extends BaseRes implements Serializable {
	@ApiModelProperty(value = "酒店Id")
	private String hotelId;
	/**
	 * 账目ID
	 */
	@ApiModelProperty(value = "账目ID")
	private String accountsId;

	/**
	 * 预定号
	 */
	@ApiModelProperty(value = "预定号")
	private String reservationNumber;

    /**
     * 账项代码
     */
    @ApiModelProperty(value = "账项代码")
    private String departmentCode;
    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

	/**
	 * 金额
	 */
	@ApiModelProperty(value = "金额", example = "88.88")
	private BigDecimal amount;

	/**
	 * 当前可用金额
	 */
	@ApiModelProperty(value = "当前可用金额", example = "88.88")
	private BigDecimal amount_curr;

	/**
	 * 预定人
	 */
	@ApiModelProperty(value = "预定人")
	private String guestName;
	
	/**
	 * 联系电话
	 */
	@ApiModelProperty(value = "联系电话")
	private String telephone;
	
	/**
	 * 渠道
	 */
	@ApiModelProperty(value = "渠道")
	private String channel;
}
