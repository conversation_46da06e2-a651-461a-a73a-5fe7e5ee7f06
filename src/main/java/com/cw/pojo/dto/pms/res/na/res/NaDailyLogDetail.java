package com.cw.pojo.dto.pms.res.na.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2024/9/11 14:49
 */
@Data
@ApiModel(description = "夜审步骤信息详情")
public class NaDailyLogDetail {

    //@ApiModelProperty(value = "夜审日期", example = "2019-09-24", required = true)
    //@JSONField(format = "yyyy-MM-dd")
    //Date naDate;
    //@ApiModelProperty(value = "开始时间", example = "2020-12-31 14:20:13", required = true)
    //String startTime;
    //@ApiModelProperty(value = "结束时间", example = "2020-12-31 14:20:13", required = true)
    //String endTime;
    //@ApiModelProperty(value = "步奏运行的总时间", example = "0ms", required = true)
    //String totalTime;

    @ApiModelProperty(value = "任务ID", example = "01", required = true)
    String progressId;
    @ApiModelProperty(value = "任务名称", example = "01", required = true)
    String progressName;
    @ApiModelProperty(value = "状态：未开始，进行中，已完成，有错误")
    String status = "未开始";
    @ApiModelProperty(value = "状态颜色")
    String color = "#8C8C8C";
    @ApiModelProperty(value = "消息，未开始-等待任务开始，进行中-开始时间，已完成-完成时间，有错误-报错信息")
    String msg = "等待任务开始";

}
