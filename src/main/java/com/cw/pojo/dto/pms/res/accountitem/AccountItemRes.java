package com.cw.pojo.dto.pms.res.accountitem;

import com.cw.pojo.dto.pms.res.BaseRes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 账项代码响应实体
 * @Author: michael.pan
 * @Date: 2024/3/24 22:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "账项代码响应实体")
public class AccountItemRes extends BaseRes implements Serializable {
    /**
     * 代码
     */
    @ApiModelProperty(value = "代码", required = true, example = "1001")
    private String code;

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID", required = true, example = "1001")
    private String hotelId;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 收入组（001客房，002餐厅，003商场，004宴会，005其他）
     */
    @ApiModelProperty(value = "收入组（001客房，002餐厅，003商场，004宴会，005其他）", example = "001")
    private String incomeGroup;

    /**
     * 今日收入
     */
    @ApiModelProperty(value = "今日收入")
    private BigDecimal incomeToday;

    /**
     * 月累计
     */
    @ApiModelProperty(value = "月累计")
    private BigDecimal monthlyTotal;
    /**
     * 年累计（9000以上为收入.9000以下为消费）
     */
    @ApiModelProperty(value = "年累计（9000以上为收入.9000以下为消费）")
    private BigDecimal yearlyTotal;


    @ApiModelProperty(value = "是否系统默认", required = true, example = "false")
    private Boolean lsys;
}
