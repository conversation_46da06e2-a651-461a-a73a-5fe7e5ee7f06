package com.cw.pojo.dto.pms.res.guest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/8/20 22:17
 * @Description 客人账目总额响应信息
 **/
@Data
@ApiModel("客人账目总额响应信息")
public class AccountTotalRes implements Serializable {
    @ApiModelProperty(value = "押金")
    private BigDecimal deposit = BigDecimal.ZERO;

    /**
     * 消费总额、保留两位小数
     */
    @ApiModelProperty(value = "消费总额（不带押金）")
    private BigDecimal consumeTotal = BigDecimal.ZERO;

    /**
     * 付款、保留两位小数
     */
    @ApiModelProperty(value = "付款总额（不带押金）")
    private BigDecimal paymentTotal = BigDecimal.ZERO;

    /**
     * 余额、保留两位小数
     */
    @ApiModelProperty(value = "余额")
    private BigDecimal balance = BigDecimal.ZERO;
}
