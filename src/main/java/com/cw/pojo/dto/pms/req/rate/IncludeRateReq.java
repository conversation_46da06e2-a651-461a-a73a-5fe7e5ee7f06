package com.cw.pojo.dto.pms.req.rate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Classname IncludeRateReq
 * @Description 包价请求实体
 * @Date 2024-03-19 21:13
 * <AUTHOR> sancho.shen
 */
@Data
@ApiModel(description = "包价信息请求对象")
public class IncludeRateReq implements Serializable {
    /**
     * 包价代码
     */
  /*  @ApiModelProperty(value = "包价代码", required = true, example = "BJ")
    @NotBlank(message = "包价代码不允许为空")
    private String code;*/

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true, example = "包价描述/名称")
    @NotBlank(message = "包价描述/名称不允许为空")
    @Length(max = 10, message = "长度不能超过10个字符")
    private String description;

    /**
     * 价格、保留两位小数
     */
    @ApiModelProperty(value = "价格", example = "0.00")
    private BigDecimal price;

    /**
     * 账项代码
     */
    @ApiModelProperty(value = "账项代码", required = true, example = "ZX")
    @NotBlank(message = "账项代码不允许为空")
    private String departmentCode;

    /**
     * 是否内含0否,1是
     */
    @ApiModelProperty(value = "是否内含0否,1是", example = "false")
    private boolean contains;


    @ApiModelProperty(value = "出账频率:0一次,1入住期间每日", example = "0")
    private Integer frequency = 0;


    @ApiModelProperty(value = "是否输出为第二天费用:默认false 当天出费用,true 标记为第二天,一般是早餐", example = "false")
    private boolean nextday = false;


    @ApiModelProperty(value = "是否与房费分开打印:默认false不分开", example = "false")
    private boolean printsep = false;

}
