package com.cw.pojo.dto.pms.res.ar;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;

import com.cw.pojo.dto.pms.req.guest.QueryARAccLogReq;
import com.cw.pojo.dto.pms.res.BaseRes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(description = "应收日志响应对象")
public class ArAccLogRes extends BaseRes implements Serializable {
	@Data
	@Accessors(chain = true)
	@ApiModel(description = "应收日志标签响应对象")
	public static class FlagMsg implements Serializable {
		@ApiModelProperty(value = "代码")
		String code;
		@ApiModelProperty(value = "值")
		String value;

		public static FlagMsg of(String code, String value) {
			return new FlagMsg().setCode(code).setValue(value);
		}
	}

	@ApiModelProperty(value = "酒店Id")
	private String hotelId;
	/**
	 * 账目ID
	 */
	@ApiModelProperty(value = "账目ID")
	private String accountsId;

	/**
	 * 账项代码
	 */
	@ApiModelProperty(value = "账项代码")
	private String departmentCode;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 金额
	 */
	@ApiModelProperty(value = "金额", example = "88.88")
	private BigDecimal amount;
	
	/**
	 * 挂账来自预定号
	 */
	@ApiModelProperty(value = "挂账来自预定号")
	private String res_no;

	/**
	 * 是否为挂账、调整
	 */
	@ApiModelProperty(value = "是否为挂账、调整")
	private boolean debit;
	/**
	 * 标签
	 */
	@ApiModelProperty(value = "标签")
	private Collection<FlagMsg> flags = new ArrayList<>();

	/**
	 * 核销明细
	 */
	@ApiModelProperty(value = "核销明细")
	private Collection<ArAccLogRes> details = new ArrayList<>();
}
