package com.cw.pojo.dto.pms.req.reservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @Classname ReservationCheckInReq
 * @Description 入住时修改预订信息
 * @Date 2024-04-22 22:04
 * <AUTHOR> sancho.shen
 */
@Data
@ApiModel(value = "入住时修改预订信息")
public class CheckinReq implements Serializable {


    //@ApiModelProperty(value = "预订关联编号", required = true)
    //private String relationNumber;

    @ApiModelProperty(value = "入住预订号信息")
    private List<String> checkinInfoList;

}
