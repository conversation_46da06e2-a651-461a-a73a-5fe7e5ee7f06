package com.cw.pojo.dto.pms.req.reservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Classname CheckoutReq
 * @Description 退房
 * @Date 2024-04-26 22:29
 * <AUTHOR> sancho.shen
 */
@Data
@ApiModel(description = "退房")
public class CheckoutReq implements Serializable {
    /*@ApiModelProperty(value = "房间号")
    private String roomNumber;
    @ApiModelProperty(value = "证件号码")
    private String idCard;*/
    @ApiModelProperty(value = "预订编号", required = true)
    @NotBlank(message = "预订编号不能为空")
    private String reservationNumber;
}
