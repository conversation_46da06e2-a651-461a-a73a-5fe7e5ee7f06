package com.cw.pojo.dto.pms.req.others.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 门锁密码删除结果
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/1/3 17:35
 **/
@Data
@ApiModel(description = "门锁密码删除结果")
public class DoorLockPasswordDeleteRes {

    @ApiModelProperty(value = "房间号")
    private String roomNo;

    @ApiModelProperty(value = "密码ID")
    private String passwordId;

    @ApiModelProperty(value = "删除是否成功")
    private boolean success = false;

    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    /**
     * 创建成功结果
     */
    public static DoorLockPasswordDeleteRes success(String roomNo, String passwordId) {
        DoorLockPasswordDeleteRes res = new DoorLockPasswordDeleteRes();
        res.setRoomNo(roomNo);
        res.setPasswordId(passwordId);
        res.setSuccess(true);
        return res;
    }

    /**
     * 创建失败结果
     */
    public static DoorLockPasswordDeleteRes failure(String errorMsg) {
        DoorLockPasswordDeleteRes res = new DoorLockPasswordDeleteRes();
        res.setSuccess(false);
        res.setErrorMsg(errorMsg);
        return res;
    }
}
