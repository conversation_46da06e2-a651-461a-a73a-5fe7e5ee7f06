package com.cw.pojo.dto.pms.req.guest;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/10/24 17:30
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "应收账户余额转移请求对象")
public class ArAccDepositTransferReq {

    @NotBlank
    @ApiModelProperty(value = "原应收账号编号", example = "1")
    private String arNo;

    @NotBlank
    @ApiModelProperty(value = "目标应收账户编号", example = "1")
    private String targetArNo;

    @NotNull
    @ApiModelProperty(value = "转账金额", example = "10000")
    private BigDecimal amount;

    @ApiModelProperty(value = "转账备注")
    private String remark;


}
