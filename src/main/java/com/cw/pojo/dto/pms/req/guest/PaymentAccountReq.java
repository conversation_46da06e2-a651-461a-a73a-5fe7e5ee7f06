package com.cw.pojo.dto.pms.req.guest;

import cn.hutool.core.collection.CollectionUtil;
import com.cw.pojo.dto.pms.req.cashier.CashierAccountReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostReq;
import com.cw.utils.ProdType;
import com.cw.utils.annotion.LockInterface;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.cw.utils.annotion.LockInterface.LockType.POST;

/**
 * <AUTHOR>
 * @Date 2024/7/16 20:25
 * @Description 付款入账请求对象
 **/
@Data
@ApiModel("付款入账请求对象")
public class PaymentAccountReq implements LockInterface {
    @ApiModelProperty(value = "预定号", example = "**********")
    private String reservationNumber;
    @ApiModelProperty(value = "后四位证件号码", example = "5678")
    private String idCard;
    @ApiModelProperty(value = "房间号码", example = "1001")
    private String roomNo;
    @ApiModelProperty(value = "支付类型-0表示线下支付、1表示线上支付", example = "0")
    private int payType = 0;

    /**
     * 9000表示现金，9100表示微信付款码支付，9101表示支付宝付款码支付
     */
    @ApiModelProperty(value = "9000表示现金，9100表示微信付款码支付，9101表示支付宝付款码支付",example = "9101")
    private String departmentCode;

    @ApiModelProperty(value = "支付方式（细分的支付方式,微信付款码传8,支付宝付款码传9）",example = "9")
    private int paymethod = 0;


    @ApiModelProperty(value = "挂账应收账号")
    private String arNo;
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额", example = "88.88")
    private BigDecimal amount;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", example = "支付宝")
    private String description;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "可乐")
    private String remark;

    public CashierPostReq toCashierPostReq() {
        CashierPostReq cashierPostReq = new CashierPostReq();
        cashierPostReq.setReservationNumber(reservationNumber);
        cashierPostReq.setRemark(remark);

        List<CashierAccountReq> lis = new ArrayList<>();
        CashierAccountReq cashierAccountReq = new CashierAccountReq();
        cashierAccountReq.setDepartmentCode(departmentCode);
        cashierAccountReq.setDescription(description);
        cashierAccountReq.setCredit(amount);
        cashierAccountReq.setRemark(remark);
        cashierAccountReq.setAr_no(arNo);

        lis.add(cashierAccountReq);


        cashierPostReq.setAccounts(lis);

        return cashierPostReq;
    }

    @Override
    public String getUniqueKey() {
        return POST+reservationNumber;
    }

    @Override
    public List<String> getUniqueKeys() {
        return new ArrayList<>();
    }
}
