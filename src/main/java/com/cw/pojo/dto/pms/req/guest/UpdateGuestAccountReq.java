package com.cw.pojo.dto.pms.req.guest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 修改客人账目请求对象
 * @Author: michael.pan
 * @Date: 2024/3/27 16:11
 */
@Data
@ApiModel(description = "修改客人账目请求对象")
public class UpdateGuestAccountReq implements Serializable {
    @NotNull
    @ApiModelProperty(value = "实体对象唯一id", example = "1")
    Long id = 0L;

    /**
     * 账项代码
     */
    @ApiModelProperty(value = "账项代码", example = "ZX")
    private String departmentCode;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 入账描述
     */
    @ApiModelProperty(value = "入账描述")
    private String description;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
