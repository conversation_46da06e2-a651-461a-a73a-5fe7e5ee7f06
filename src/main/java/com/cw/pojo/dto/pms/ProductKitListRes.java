package com.cw.pojo.dto.pms;

import com.cw.entity.Productkit;
import com.cw.pojo.dto.common.res.PageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "套餐列表返回")
public class ProductKitListRes extends PageResponse<ProductKitListRes.ProductKitListData, Productkit> {

    @Data
    public static class ProductKitListData {
        @ApiModelProperty(value = "主键 ID")
        private Long id;
        @ApiModelProperty(value = "套餐名称")
        private String name;
        @ApiModelProperty(value = "套餐明细售价")
        private BigDecimal totalPrice;
        @ApiModelProperty(value = "套餐基础售价")
        private BigDecimal price;
        @ApiModelProperty(value = "每日库存")
        private Integer dailyInventory;
        @ApiModelProperty(value = "售卖期")
        private String saleDateRange;
        @ApiModelProperty(value = "状态 (1:启用, 0:禁用)")
        private Integer status;
    }
} 