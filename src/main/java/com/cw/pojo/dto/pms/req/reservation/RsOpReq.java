package com.cw.pojo.dto.pms.req.reservation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel(value = "预订操作请求")
public class RsOpReq implements Serializable {


    @ApiModelProperty(value = "预订编号", required = true)
    @NotBlank(message = "预订编号不能为空")
    private String reservationNumber;
}
