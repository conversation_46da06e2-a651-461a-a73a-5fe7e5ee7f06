package com.cw.pojo.dto.pms.req.room;

import com.cw.pojo.dto.common.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "门锁查询列表请求")
public class DoorlockPageReq extends PageReq {
    @ApiModelProperty(value = "房间号", example = "110")
    String roomNo;

    @ApiModelProperty(value = "门锁编码", example = "33:444:55")
    String lockNo;

}

