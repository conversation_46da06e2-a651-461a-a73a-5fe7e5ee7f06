package com.cw.pojo.dto.pms.req.hotel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * @Classname HotelInfoRequest
 * @Description 酒店信息请求对象
 * @Date 2024-03-16 1:48
 * <AUTHOR> sancho.shen
 */
@Data
@ApiModel(description = "酒店信息对象")
public class HotelInfoReq implements Serializable {

    @NotBlank(message = "酒店名不允许为空")
    @ApiModelProperty(value = "酒店名称", example = "TEST", required = true)
    private String hotelName;

    @NotBlank(message = "酒店地址不允许为空")
    @ApiModelProperty(value = "酒店地址", example = "海城区火车桥", required = true)
    private String address;

    @ApiModelProperty(value = "描述")
    private String description;

    @NotBlank(message = "经营人不允许为空")
    @ApiModelProperty(value = "经营人", example = "老板A", required = true)
    private String operator;

    @NotBlank(message = "身份证不允许为空")
    @Pattern(regexp = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)", message = "身份证格式不正确")
    @ApiModelProperty(value = "身份证", example = "******************", required = true)
    private String idCard;

    @NotBlank(message = "营业执照不允许为空")
    @ApiModelProperty(value = "营业执照/图片地址/上传图片到OSS后返回的地址", example = "https://xxxxx.com/xxx.png", required = true)
    private String businessLicense;

    @Min(message = "应不低于1间房", value = 1)
    @ApiModelProperty(value = "房间总数", example = "200")
    private int roomTotal;

    @NotBlank(message = "联系电话不允许为空")
    @ApiModelProperty(value = "联系电话", example = "***********")
    private String telephone;

    @ApiModelProperty(value = "备注")
    private String remark;

    @NotBlank(message = "品牌ID不允许为空")
    @ApiModelProperty(value = "品牌ID", example = "**********")
    private String brandId;

    @ApiModelProperty(value = "协议条款")
    private AgreementInfo agreement;

    @ApiModelProperty(value = "周末定义")
    private String weekendDefinition;

    @ApiModelProperty(value = "收款码图片集合")
    private List<QrCodeInfo> qrInfos;

    @ApiModelProperty(value = "自助模式")
    private SelfMode selfModes;

    @ApiModel(description = "收款码图片集合")
    @Data
    public static class QrCodeInfo {
        @ApiModelProperty(value = "收款码图片地址，上传图片到OSS后返回的地址", required = true)
        private String qrCode;

        @ApiModelProperty(value = "收款码类型,微信支付->0,支付宝->1,9->银联聚合码", required = true)
        private int qrType;
    }

    @ApiModel(description = "协议条款")
    @Data
    public static class AgreementInfo {
        @ApiModelProperty(value = "隐私协议")
        private String privacy;

        @ApiModelProperty(value = "预订须知")
        private String instructions;
    }

    @ApiModel(description = "自助模式")
    @Data
    public static class SelfMode {


        @ApiModelProperty(value = "全日房价格政策", required = true)
        private String customRate;

        @ApiModelProperty(value = "钟点房价格政策", required = true)
        private String hourRate;

        @ApiModelProperty(value = "可售房间类型,多个以逗号分隔,为空则全选", required = true)
        private String roomTypes;

        @ApiModelProperty(value = "紧急联系电话")
        private String telephone;

        @ApiModelProperty(value = "钟点房可售开始时间, HH:mm", example = "18:00")
        private String beginTime;

        @ApiModelProperty(value = "钟点房可售结束时间, HH:mm", example = "00:00")
        private String endTime;

        @ApiModelProperty(value = "副屏轮播图,多个以逗号分隔")
        private String secondScreenPics;

    }
}
