package com.cw.pojo.dto.pms.req.invoice;

import com.cw.pojo.dto.common.req.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 查询发票管理请求实体
 * @Author: michael.pan
 * @Date: 2024/3/30 17:26
 */
@Data
@ApiModel(description = "查询发票管理请求实体")
public class QueryInvoiceManageReq extends PageReq implements Serializable {
    /**
     * 公司名
     */
    @ApiModelProperty(value = "公司名", example = "XXX有限公司", required = true)
    private String companyName;
    /**
     * 税号
     */
    @ApiModelProperty(value = "税号", required = true)
    private String taxId;

    /**
     * 银行账户
     */
    @ApiModelProperty(value = "银行账户", example = "6288888888888xxx", required = true)
    private String blankAccount;
}
