package com.cw.pojo.dto.pms.res.oss;

import com.aliyun.oss.model.ObjectMetadata;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2022-09-09
 */
@Data
@ApiModel(description = "oss文件元信息")
public class OssObjectInfo {
    @ApiModelProperty(value = "自定义元信息集合")
    Map<String, String> userMetadata;
    @ApiModelProperty(value = "电信OSS对象媒体信息")
    ObjectMetadata metadata;
}
