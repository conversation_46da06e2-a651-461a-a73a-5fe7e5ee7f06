package com.cw.pojo.dto.pms.req.cashier;

import com.cw.utils.CalculateNumber;
import com.cw.utils.enums.AccountItemEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(description = "应收核算前查询请求")
public class ArWriteOffQueryReq {

    /**
     * 参与核销帐的ID
     */
    @ApiModelProperty(value = "参与核销帐的ID")
    @NotEmpty(message = "无核销记录")
    private Collection<String> acc_ids = Lists.newArrayList();


    @ApiModelProperty(value = "应收账号 (细分的支付方式为应收挂账时使用)")
    private String ar_no;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
