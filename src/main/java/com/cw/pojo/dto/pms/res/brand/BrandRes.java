package com.cw.pojo.dto.pms.res.brand;

import com.cw.pojo.dto.pms.res.BaseRes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Classname BrandRes
 * @Description 品牌响应实体
 * @Date 2024-03-20 20:59
 * <AUTHOR> sancho.shen
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "品牌响应信息")
public class BrandRes extends BaseRes implements Serializable {
    @ApiModelProperty(value = "品牌名称", example = "如新旅游公司")
    private String brandName;

    @ApiModelProperty(value = "描述")
    private String description;
}
