package com.cw.pojo.dto.pms.res.guest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/16 20:30
 * @Description 入账响应对象
 **/
@Data
@ApiModel("入账响应对象")
public class AccountRes implements Serializable {
    @ApiModelProperty(value = "入账状态码，0表示入账成功，其他表示失败", example = "0")
    private int code;
    @ApiModelProperty(value = "描述", example = "success")
    private String msg;
}
