package com.cw.pojo.dto.pms.req.rate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Classname IncludePriceReq
 * @Description 房价信息请求实体
 * @Date 2024-03-18 23:27
 * <AUTHOR> sancho.shen
 */
@Data
@ApiModel(description = "房价信息请求对象")
public class RoomRateEntity implements Serializable {

    /**
     * 房价代码
     */
    /*@ApiModelProperty(value = "房价代码", required = true, example = "FJ")
    @NotBlank(message = "房价代码不允许为空")
    private String code;*/

    @ApiModelProperty(value = "主键ID", name = "id", example = "1", required = true)
    //@NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * 房价描述
     */
    @ApiModelProperty(value = "房价描述/名称 控制在40个字符以内？", required = true)
    @NotBlank(message = "房价描述/名称不允许为空")
    @Length(max = 40, message = "长度不能超过40个字符")
    private String description;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间 yyyy-MM-dd", required = true, example = "2024-03-18")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间 yyyy-MM-dd", required = true, example = "2024-04-18")
    private String endTime;

    /**
     * 账项代码
     */
    @ApiModelProperty(value = "账项代码", required = true, example = "1001")
    private String departmentCode;

    /**
     * 包价代码
     */
    @ApiModelProperty(value = "包价代码", example = "BZ", required = true)
    private String includeCode;


    /**
     * 频率
     * 1->每分钟,2->每小时，3->每日,4->每周，5->每月
     */
    @ApiModelProperty(value = "频率:1->每分钟,2->每小时，3->每日,4->每周，5->每月", example = "2", required = true)
    private int frequency;

    @ApiModelProperty(value = "间隔小时数(算钟点房)", example = "2")
    private int intervalHour;

}
