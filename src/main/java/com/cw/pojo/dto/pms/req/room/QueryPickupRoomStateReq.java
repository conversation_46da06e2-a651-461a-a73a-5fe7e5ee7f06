package com.cw.pojo.dto.pms.req.room;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @Date 2024/6/18 22:18
 * @Description 查询远期占用房态请求对象
 **/
@Data
@ApiModel("查询远期占用房态请求对象")
public class QueryPickupRoomStateReq {
    @ApiModelProperty(value = "房型", example = "HHDCF")
    private String roomType;

    @ApiModelProperty(value = "查询远期占用房态日期", example = "2019-03-27")
    private String futureDate;
}
