package com.cw.pojo.dto.pms.req.oss;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Describe oss查询目录下文件
 * <AUTHOR> <PERSON>
 * @Create on 2021/11/22 0022
 */
@Data
@ApiModel(description = "oss查询目录下文件请求对象")
public class OssQueryReq {
    @ApiModelProperty(value = "查询目录路径，以‘/’结尾标识仅获取当前目录下的二级目录和文件", example = "banner/", required = true)
    String path;

    @ApiModelProperty(value = "查询当前目录下文件名称")
    String searchKey;

    @ApiModelProperty(value = "查询当前目录下文件格式,下拉框-ossformat，过滤tab参数：material-图片，video-视频,audio-音频")
    String format;

}
