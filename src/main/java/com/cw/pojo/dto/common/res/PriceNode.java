package com.cw.pojo.dto.common.res;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2019/12/20 15:14
 **/
@Data
@ApiModel(value = "每日房价节点")
public class PriceNode {
    @ApiModelProperty(value = "价格")
    BigDecimal price;
    @ApiModelProperty(value = "日期", example = "2019-09-24")
    @JSONField(format = "yyyy-MM-dd")
    Date date;

    public PriceNode() {

    }

    public PriceNode(Date date, BigDecimal price) {
        this.price = price;
        this.date = date;
    }
}
