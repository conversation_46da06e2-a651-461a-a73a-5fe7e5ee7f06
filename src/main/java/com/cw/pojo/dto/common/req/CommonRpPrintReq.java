package com.cw.pojo.dto.common.req;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

@Data
@ApiModel(description = "通用报表打印请求参数")
public class CommonRpPrintReq {
    //@ApiModelProperty(value = "查询代码", example = "code")
    //String searchKey;

    @ApiModelProperty(value = "报表ID", example = "1")
    String rpId;

    @JSONField(format = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始日期", example = "2025-02-01")
    LocalDate startDate;

    @JSONField(format = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束日期", example = "2025-02-06")
    LocalDate endDate;


    @ApiModelProperty(value = "报表选项", example = "1")
    String option;


}

