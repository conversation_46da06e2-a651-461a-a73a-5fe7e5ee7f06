package com.cw.pojo.dto.common.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "更新记录排序请求")
public class Common_UpdSeq_Req {
    @ApiModelProperty(value = "要更新的表数据类型实体", notes = "RoomType,Hotel", example = "RoomType", required = true)
    String tableName;

    @ApiModelProperty(value = "实体对象唯一id", example = "1", required = true)
    Long id = 0L;

    //@ApiModelProperty(value = "范围字段名", notes = "比如菜单是在 orgmenuid 字段范围内排序,菜单内容是在 menuid 范围内排序", example = "2", required = true)
    //String groupColumn;
    //
    //@ApiModelProperty(value = "范围字段值", notes = "范围字段值,对应 orgmenuid,或者 menuid的值", example = "2", required = true)
    //String rangeValue;

    @ApiModelProperty(value = "范围内字段名对应字段值的集合", notes = "条件约束", example = "2", required = true)
    Map<String, Object> columnMap;

    @ApiModelProperty(value = "往前排传up,往后排传down", required = true)
    Boolean lup = true;

    @ApiModelProperty(value = "修复.将当前数据从0---9999排列,数字越小排越前面", example = "")
    Boolean lFix = false;

}

