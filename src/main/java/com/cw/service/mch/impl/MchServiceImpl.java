package com.cw.service.mch.impl;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.HotelCache;
import com.cw.config.exception.CustomException;
import com.cw.config.satoken.MchStpUtil;
import com.cw.entity.AppUser;
import com.cw.entity.GroupBand;
import com.cw.entity.Hotel;
import com.cw.mapper.AppUserMapper;
import com.cw.mapper.GroupBandMapper;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.mch.req.MchUserHotelInfoReq;
import com.cw.pojo.dto.mch.req.MchUserSwitchHotelReq;
import com.cw.pojo.dto.mch.res.MchUserHotelInfo;
import com.cw.pojo.dto.mch.res.MchUserResult;
import com.cw.pojo.dto.pms.req.mch.MchUserLoginReq;
import com.cw.service.context.GlobalContext;
import com.cw.service.context.MchContext;
import com.cw.service.mch.MchService;
import com.cw.utils.ContentCacheTool;
import com.cw.utils.LoginJwtForm;
import com.cw.utils.enums.AgentType;
import com.cw.utils.enums.EntranceType;
import com.cw.utils.enums.GlobalDataType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Describe 商家端实现类
 * <AUTHOR> Tony Leung
 * @Create on 2025/6/24 14:17
 */
@Service
@Slf4j
public class MchServiceImpl implements MchService {

    @Autowired
    private AppUserMapper appUserMapper;
    @Autowired
    private GroupBandMapper groupBandMapper;

    /**
     * 获取用户
     *
     * @return
     */
    @Override
    public List<MchUserHotelInfo> getUserHotelList(MchUserHotelInfoReq req) {
        List<MchUserHotelInfo> list = new ArrayList<>();
        //后端获取当前正在操作的酒店ID
        //后端获取前端当前登陆用户
        String groupMobile = req.getGroupMobile();
        //todo 可能重复
        AppUser appUser = appUserMapper.findAppUserByMobileNo(groupMobile);
        // 使用缓存
        if (appUser != null) {
            if (!appUser.getOStatus()) {
                //被禁用是否直接返回
            }
            String groupId = appUser.getGroupId();
            if (StringUtils.isNotBlank(appUser.getGroupId())) {

                GroupBand groupBand = groupBandMapper.findGroupBandByCode((groupId));
                if (groupBand != null && StringUtils.isNotBlank(groupBand.getHotelIds())) {
                    MchUserHotelInfo groupBandInfo = new MchUserHotelInfo();
                    //ALL  为了避免冲突重复 ，集团代码是否需要固定
                    groupBandInfo.setHotelId(groupBand.getCode());
                    groupBandInfo.setHotelName(groupBand.getDescription());
                    groupBandInfo.setType(0);
                    //第一个集团代码位置
                    list.add(0, groupBandInfo);
                    List<String> hotelIdList = Arrays.asList(groupBand.getHotelIds().split(","));
                    HotelCache hotelCache = GlobalCache.getDataStructure().getCache(GlobalDataType.HOTEL);
                    for (String hotelId : hotelIdList) {
                        Hotel hotel = hotelCache.getRecord(hotelId, hotelId);
                        if (hotel != null) {
                            MchUserHotelInfo hotelInfo = new MchUserHotelInfo();
                            BeanUtil.copyProperties(hotel, hotelInfo);
                            hotelInfo.setType(1);
                            list.add(hotelInfo);
                        }
                    }
                }
            }
            //使用缓存

        }

        return list;
    }

    /**
     * 集团登录token
     *
     * @param mchUserLoginReq
     * @return
     */
    @Override
    public MchUserResult loginMchUser(MchUserLoginReq mchUserLoginReq) {
        LoginJwtForm form = new LoginJwtForm();
        form.setEntranceType(EntranceType.WEBAPP);
        form.setAgentType(AgentType.PC);
        form.setGroupMobile(mchUserLoginReq.getMobileNo());
        AppUser appUser = appUserMapper.findAppUserByMobileNo(mchUserLoginReq.getMobileNo());
        if (appUser != null) {
            if (!appUser.getOStatus()) {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("用户已禁用.请联系管理员"));
            }
            //明文密码和数据库密文比较
            if (new BCryptPasswordEncoder().matches(mchUserLoginReq.getEncryptPwd(), appUser.getPwd()) && StrUtil.isNotBlank(appUser.getPwd())) {
                //通过手机号获取用户和对应景区大组代码
                form.setUserId(appUser.getUserid());
                //设置hotelid有问题
                form.setHotelId(appUser.getGroupId());
                MchStpUtil.login(form);//框架内登陆 REDIS 方式
            } else {
                throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("用户名或密码错误"));
            }
        } else {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("用户名或密码错误"));
        }

        MchUserResult result = new MchUserResult();
        SaTokenInfo tokenInfo = MchStpUtil.getTokenInfo();
        String token = tokenInfo.getTokenValue();
        result.setToken(token);
        result.setType(0);
        result.setUserid(appUser.getUserid());
        result.setGroupMobile(mchUserLoginReq.getMobileNo());
        return result;
    }


    /**
     * 切换门店登录
     *
     * @param req
     * @return
     */
    @Override
    public MchUserResult loginSwitchHotel(MchUserSwitchHotelReq req) {
        MchUserResult userResult = new MchUserResult();
        String groupMobile = "";
        String userid = "";
        String hotelId = "";
        EntranceType type = EntranceType.WEBAPP;
        EntranceType currentType = EntranceType.WEBAPP;
        if (req.getCurrentType() != null) {
            if (req.getCurrentType() == 0) {
                currentType = EntranceType.WEBAPP;
                userid = MchContext.getCurrentUserId();
                hotelId = MchContext.getCurrentHotelId();
                groupMobile = MchContext.getCurrentGroupMobile();

            } else if (req.getCurrentType() == 1) {
                //门店运营
                currentType = EntranceType.CONSOLEWEB;
                userid = GlobalContext.getCurrentUserId();
                hotelId = GlobalContext.getCurrentHotelId();
                groupMobile = GlobalContext.getCurrentGroupMobile();

            }
        }

        //如果是当前集团重复操作 直接返回 还是刷新token
        if (!ContentCacheTool.isMchHotel(groupMobile, req.getHotelId(), req.getType())){
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("当前用户未关联跳转酒店"));
        }

        if (StringUtils.isBlank(userid)) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("token已过期"));
        }
        hotelId = req.getHotelId();

        if (req.getType() == 0) {
            type = EntranceType.WEBAPP;
        } else if (req.getType() == 1) {
            type = EntranceType.CONSOLEWEB;
        }

        LoginJwtForm loginJwtForm = new LoginJwtForm();
        loginJwtForm.setUserId(userid);
        loginJwtForm.setEntranceType(type);
        loginJwtForm.setHotelId(hotelId);
        loginJwtForm.setAgentType(AgentType.PC);
        loginJwtForm.setGroupMobile(groupMobile);

        String token = generateTokenByFormAndLogin(loginJwtForm, currentType);
        userResult.setToken(token);
        userResult.setGroupMobile(groupMobile);
        //标识
        userResult.setType(EntranceType.WEBAPP.equals(type) ? 0 : 1);
        return userResult;
    }

    /**
     * 对应不同用户端返回的token数据
     *
     * @param loginJwtForm
     * @param currentType
     * @return
     */
    private String generateTokenByFormAndLogin(LoginJwtForm loginJwtForm, EntranceType currentType) {
        String token = null;
        //当前token登出
        if (currentType == EntranceType.WEBAPP) {
            MchStpUtil.logout();
        } else if (currentType == EntranceType.CONSOLEWEB) {
            StpUtil.logout();
        }
        switch (loginJwtForm.getEntranceType()) {
            case WEBAPP:
                //对应最新token登入
                MchStpUtil.login(loginJwtForm);
                SaTokenInfo mchToken = MchStpUtil.getTokenInfo();
                token = mchToken.getTokenValue();
                break;
            case CONSOLEWEB:
                //对应最新token登入
                StpUtil.logout();
                StpUtil.login(loginJwtForm);
                SaTokenInfo consoleTokenInfo = StpUtil.getTokenInfo();
                token = consoleTokenInfo.getTokenValue();
            default:
                break;
        }
        return token;
    }
}
