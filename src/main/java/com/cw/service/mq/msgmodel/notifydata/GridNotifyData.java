package com.cw.service.mq.msgmodel.notifydata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/10/24 22:15
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GridNotifyData {
    private String enddate;
    private String startdate;
    private String hotelId;
    private String type;// roomtypelist,roomtype
    private Integer num = 0;//
    private String prodcode;
    private String productGroup;

    /*public void fillList(String type, String product, String productGroup) {//一次只传一个product
        ProductNode node = new ProductNode();
        if (type.equals(SystemUtil.ProductType.ROOM)) {
            this.type = SystemUtil.NotifyType.ROOM;
            node.setRoomtype(product);
            node.setHotel(productGroup);
            roomtypelist = new ArrayList<>();
            roomtypelist.add(node);
        } else if (type.equals(SystemUtil.ProductType.CANYIN)) {
            this.type = SystemUtil.NotifyType.CANYIN;
            node.setRestaurantcode(product);
            poslist = new ArrayList<>();
            poslist.add(node);
        } else if (type.equals(SystemUtil.ProductType.TICKET)){
            this.type = SystemUtil.NotifyType.TICKET;
            node.setTicketcode(product);
            ticketlist = new ArrayList<>();
            ticketlist.add(node);
        } else if (type.equals(SystemUtil.ProductType.TAOCAN)){
            this.type = SystemUtil.NotifyType.TAOCAN;
            node.setPackagecode(product);
            packages = new ArrayList<>();
            packages.add(node);
        }
    }*/

    @Data
    private class ProductNode {
        private int num;
        private String roomtype;
        private String hotel;
        private String restaurantcode;
        private String ticketcode;
        private String packagecode;
    }
}
