package com.cw.service.mq.comsumer.notify;

import cn.hutool.json.JSONObject;
import com.cw.service.mq.MqNameUtils;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/9/30 15:04
 **/
@Component
public class ConfigNotifyConsumer extends BaseNotifyConsumer implements ChannelAwareMessageListener {

    public ConfigNotifyConsumer() {
        super(MqNameUtils.NotifyOp.CONFIG, JSONObject.class);
    }

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody());
        //System.out.println("Received config notify message:  请填充好内容后进行发送" + messageBody);

        brocastConsume(messageBody);

    }


}
