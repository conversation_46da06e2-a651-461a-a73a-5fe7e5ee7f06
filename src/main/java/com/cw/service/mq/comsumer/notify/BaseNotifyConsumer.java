package com.cw.service.mq.comsumer.notify;

import com.alibaba.fastjson.JSON;
import com.cw.config.GroupServerConfig;
import com.cw.service.mq.ExpireMessagePostProcessor;
import com.cw.service.mq.MqNameUtils;
import com.cw.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

@Slf4j
public abstract class BaseNotifyConsumer {
    private static Logger logger = LoggerFactory.getLogger(BaseNotifyConsumer.class);

    private static HttpHeaders headers = null;
    protected MqNameUtils.NotifyOp op;
    protected Class<?> notifyMsgClass = null;
    protected Class<?> pushMsgClass = null;
    @Autowired
    protected RestTemplate restTemplate;
    @Autowired
    protected GroupServerConfig groupServerConfig;

    // private String successNode = "message";
    // private String successVal = "ok";
    private String successNode = "msg";
    private String successVal = "成功";

    /**
     * 通知消息对应的string 要转的类型
     *
     * @param op
     * @param notifyMsgClass
     */
    public BaseNotifyConsumer(MqNameUtils.NotifyOp op, Class notifyMsgClass) {
        this.notifyMsgClass = notifyMsgClass;
        this.op = op;
    }

    protected HttpHeaders getPostHeader() {
        if (headers == null) {
            headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json;charset=UTF-8"));
        }
        return headers;
    }


    protected void brocastConsume(String msgBody) {
        try {
            handleMsg(msgBody); //根据具体项目.具体推送地址.进行推送.
        } catch (Exception e) {
            //e.printStackTrace();
            handlerError(msgBody);
        }
    }

    public void handleMsg(String msg) throws Exception {
        if (true) {
            //log.info("推送消息:" + msg);
            //注释 让推送可以通知
            // return;
        }

        HttpEntity<String> httpEntity = new HttpEntity<>(msg, getPostHeader());
        ResponseEntity<String> responseEntity = restTemplate.exchange(getNotifyUrl(), HttpMethod.POST, httpEntity, String.class);
        String body = responseEntity.getBody();
        boolean lok = responseEntity.getStatusCode().is2xxSuccessful() &&
                successVal.equals(JSON.parseObject(StringUtils.defaultString(body, "{}")).get(successNode));
        if (!lok) {
            throw new Exception("推送通知失败");
        }
    }

    protected void handlerError(String msg) {
        RabbitTemplate rabbitTemplate = SpringUtil.getBean("rabbitTemplate", RabbitTemplate.class);
        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);

        String mapKey = "notify_" + op;
        RMapCache<String, Integer> map = redissonClient.getMapCache(mapKey);
        Integer currNum = map.get(msg);
        currNum = currNum == null ? 1 : currNum + 1;

        int trycount = 10; //默认重试10次
        boolean lfail = false;
        if (currNum > trycount) { //先定义.重试10次重发为失败
            lfail = true;
            logger.error("通知失败 消息内容:{} 重试{} 次", op.name(), msg, trycount);
        }
        if (lfail) {
            map.remove(msg);
            logger.error("[PUSH] {} 通知失败 消息内容:{}", op.name(), msg);
        } else {
            map.put(msg, currNum, 1, TimeUnit.HOURS); //6小时
            String delaySignal = MqNameUtils.getGroupDelayQueueSignal(op);
            int interval = 3;  //3秒后重新入队
            ExpireMessagePostProcessor expire = new ExpireMessagePostProcessor(interval, TimeUnit.SECONDS);
            rabbitTemplate.convertAndSend(MqNameUtils.DirectExchange.DELAY.name(), delaySignal, msg, expire);
        }
    }

    protected String getNotifyUrl() {
        String baseUrl = groupServerConfig.getMqurl();
        String url = baseUrl;
        if (this.op.equals(MqNameUtils.NotifyOp.ROOMRATE)) {
            url = url + "/sync/ratedet";
        } else if (this.op.equals(MqNameUtils.NotifyOp.ROOMGRID)) {
            url = url + "/sync/sku";
        } else if (this.op.equals(MqNameUtils.NotifyOp.ORDER)) {
            url = url + "/sync/order/room";
        }
        return url;
    }
}
