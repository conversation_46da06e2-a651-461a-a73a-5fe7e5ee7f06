package com.cw.service.mq.comsumer;

import com.alibaba.fastjson.JSON;
import com.cw.config.ScheduleWorkConfig;
import com.cw.service.mq.ScheduleMsg;
import com.cw.service.scheduletask.job.CrsCronNaJob;
import com.cw.utils.SpringUtil;
import com.cw.utils.SystemUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.JobDetailImpl;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.quartz.impl.triggers.SimpleTriggerImpl;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ReScheduleConsumer implements ChannelAwareMessageListener {


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String msg = new String(message.getBody());
        log.info("change schedule  " + msg);
        ScheduleMsg model = JSON.parseObject(msg, ScheduleMsg.class);
        switch (model.getOpType()) {
            case SystemUtil.ScheduleOp.SIMPLE_TRIGGER:
                flushSimpleJob(model);
                break;
            case SystemUtil.ScheduleOp.CRON_TRIGGER:
                flushCronJob(model);
                break;
            case SystemUtil.ScheduleOp.TRIGGER_NOW:
                triggerJob(model);
                break;
            case SystemUtil.ScheduleOp.DELETE_TRIGGER:
                deleteJob(model);
                break;
            default:
                return;
        }
    }

    /**
     * 刷新固定间隔任务的时间间隔.
     *
     * @param model
     */
    private void flushSimpleJob(ScheduleMsg model) {
        try {
            Scheduler scheduler = SpringUtil.getBean(ScheduleWorkConfig.quartzSchedulerName, Scheduler.class);
            TriggerKey key = TriggerKey.triggerKey(model.getTriggerName(), model.getTriggerGroup());
            Trigger trigger = scheduler.getTrigger(key);
            if (trigger != null && trigger instanceof SimpleTriggerImpl) {
                SimpleTriggerImpl simpleTrigger = (SimpleTriggerImpl) trigger;
                simpleTrigger.setRepeatInterval(TimeUnit.MINUTES.toMillis(model.getInternal()));
                scheduler.rescheduleJob(key, simpleTrigger);
                log.info("{} 定时任务 group:{} name:{} 成功", "刷新simple定时", model.getTriggerGroup(), model.getTriggerName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void deleteJob(ScheduleMsg model) {
        try {
            Scheduler scheduler = SpringUtil.getBean(ScheduleWorkConfig.quartzSchedulerName, Scheduler.class);
            TriggerKey key = TriggerKey.triggerKey(model.getTriggerName(), model.getTriggerGroup());
            Trigger trigger = scheduler.getTrigger(key);
            if (trigger != null) {
                scheduler.deleteJob(trigger.getJobKey());
                log.info("{} 定时任务 group:{} name:{} 成功", "删除", model.getTriggerGroup(), model.getTriggerName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void triggerJob(ScheduleMsg model) {
        try {
            Scheduler scheduler = SpringUtil.getBean(ScheduleWorkConfig.quartzSchedulerName, Scheduler.class);
            TriggerKey key = TriggerKey.triggerKey(model.getTriggerName(), model.getTriggerGroup());
            Trigger trigger = scheduler.getTrigger(key);
            if (trigger != null) {
                scheduler.triggerJob(trigger.getJobKey());
                log.info("{} 定时任务 group:{} name:{} 成功", "触发执行", model.getTriggerGroup(), model.getTriggerName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    /**
     * 定时任务夜审
     * @param model
     */
    private void flushCronJob(ScheduleMsg model) {
        try {
            Scheduler scheduler = SpringUtil.getBean(ScheduleWorkConfig.quartzSchedulerName, Scheduler.class);
            TriggerKey key = TriggerKey.triggerKey(model.getTriggerName(), model.getTriggerGroup());
            Trigger trigger = scheduler.getTrigger(key);
            //如果是首次保存.添加夜审任务的话.
            if (trigger == null) {
                JobDetailImpl job = new JobDetailImpl();
                job.setGroup(model.getJobGroup());
                job.setName(model.getJobName());
                job.setJobClass(CrsCronNaJob.class);
                CronTriggerImpl cronTrigger = new CronTriggerImpl();
                cronTrigger.setName(model.getTriggerName());
                cronTrigger.setGroup(model.getTriggerGroup());
                cronTrigger.setDescription("夜审线程");
                //每日都定时释放
                if (model.getCronTime().isEmpty()) {
                    //每日晚上12点开始执行  0 30 0 * * ?
                    cronTrigger.setCronExpression("0 30 0 * * ?");
                } else {   //按设定的计划日期释放s
                    // CalculateDate.getCronExp(channeldet.getCutdate());
                    String cronExp = model.getCronTime();
                    if (!CronExpression.isValidExpression(cronExp)) {
                        throw new Exception("错误的日期表达式: " + cronExp);
                    }
                    cronTrigger.setCronExpression(cronExp);
                }
                scheduler.scheduleJob(job, cronTrigger);
            } else {//已经存在定时器的.就是刷新执行时间
                CronTrigger cronTrigger = (CronTrigger) trigger;
                CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(model.getCronTime());
                TriggerBuilder<Trigger> triggerBuilder = TriggerBuilder.newTrigger();
                triggerBuilder.withIdentity(key);
                triggerBuilder.withSchedule(scheduleBuilder);
                //保留原来线程的描述
                triggerBuilder.withDescription(cronTrigger.getDescription());
                trigger = triggerBuilder.build();
                scheduler.rescheduleJob(key, trigger);
            }
            log.info("{} 定时任务 group:{} name:{} 成功", "刷新自定义定时任务", model.getTriggerGroup(), model.getTriggerName());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }




}
