package com.cw.service.na.tasks.impl;


import com.cw.entity.NaDailyLog;
import com.cw.entity.NaRunInfo;
import com.cw.mapper.NaRunInfoMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.service.scheduletask.ElectionService;
import com.cw.utils.CalculateDate;
import com.cw.utils.LoggerUtil;
import com.cw.utils.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 资源表日常任务
 * Created by flyhigh on 2016/12/22.
 */
public class ResourceDailyTask extends BaseDailyTask {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Override
    public boolean runTask(NaRunInfo naRunInfo){
        String status = "成功";
        String msg = "完成";
        long s1 = System.currentTimeMillis();
        ElectionService electionService = SpringUtil.getBean(ElectionService.class);
        String ip = electionService.getAppSignal();
        DaoLocal<?> daoLocal = SpringUtil.getBean(DaoLocal.class);
        NaRunInfoMapper naRunInfoMapper = SpringUtil.getBean(NaRunInfoMapper.class);
        try {
            naRunInfo.setRunProgress(processId  + "");
            naRunInfoMapper.saveAndFlush(naRunInfo);
            naService.executeResource(naTaskInfo);
        } catch (RuntimeException e) {
            e.printStackTrace();
            String exception = "夜审RUN_TIME_EXCEPTION, " + LoggerUtil.getExceptionDesc(e, false);
            logger.error(exception);
            status = "失败";
            msg = exception;
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            String exception = "夜审报错, " + LoggerUtil.getExceptionDesc(e, false);
            logger.error(exception);
            status = "失败";
            msg = exception;
            return false;
        } finally {
            long s2 = System.currentTimeMillis();
            long total = s2 - s1;
            NaDailyLog nadailylog = naTaskInfo.getNadailylog();
            nadailylog.setStartTime(CalculateDate.spd_time.format(s1));
            nadailylog.setEndTime(CalculateDate.spd_time.format(s2));
            nadailylog.setTotalTime(total + "");
            nadailylog.setStatus(status);
            nadailylog.setMsg(msg);
            nadailylog.setRunIp(ip);
            daoLocal.merge(nadailylog);
        }
        return true;
    }
}
