package com.cw.service.scheduletask.job.jobpropertie;


import com.cw.service.scheduletask.enums.CrsCronJobType;
import com.cw.service.scheduletask.job.BaseCrsJob;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/8/7 9:24
 **/
@Data
public abstract class BaseCrsCronJobProp {
    private CrsCronJobType crsJobType;
    private long interval = 5000;
    private Class<? extends BaseCrsJob> jobClass;
    private String threadDesc = "";

    public BaseCrsCronJobProp(CrsCronJobType crsCronJobType,
                              Class<? extends BaseCrsJob> jobClass,
                              String threadDesc) {
        this.crsJobType = crsCronJobType;
        this.jobClass = jobClass;
        this.threadDesc = threadDesc;
    }

}
