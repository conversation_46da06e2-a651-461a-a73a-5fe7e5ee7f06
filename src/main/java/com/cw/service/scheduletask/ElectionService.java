package com.cw.service.scheduletask;

import com.cw.utils.RedisKey;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.InetAddress;
import java.util.concurrent.TimeUnit;

/**
 * 心跳执行.每10秒竞争一次锁.
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2020/12/14 08:58
 **/
@Service
@Slf4j
public class ElectionService {

    @Autowired
    RedissonClient redissonClient;
    boolean lMaster;
    String appSignal;
    RLock masterLock;
    @Value("${random.value}")
    private String randomSignal;
    @Autowired
    private Environment env;

    public boolean isMasterNode() {
        return lMaster;
    }

    public String getAppSignal() {
        return appSignal;
    }

    @PostConstruct
    public void init() {
        try {
            String ip = InetAddress.getLocalHost().getHostAddress();
            String port = env.getProperty("server.port");
            appSignal = ip + ":" + port + ":" + randomSignal;
        } catch (Exception e) {
            appSignal = RedisKey.Lock_ElectionMasterLock + randomSignal;
        }
    }

    @Scheduled(cron = "0/10 * * * * ?")
    public void heartBeat() {
        tryHold();
    }

    public void tryHold() {
        masterLock = redissonClient.getLock(RedisKey.Lock_ElectionMasterLock);
        boolean lsucess = false;
        try {
            lsucess = masterLock.tryLock(15, TimeUnit.SECONDS); //心跳间隔10秒.尝试加锁15秒. 15秒后自动解锁.或者关闭应用时自动解锁
        } catch (InterruptedException e) {
//            e.printStackTrace();
        }
        lMaster = lsucess;

        if (lMaster) {

        } else {

        }
    }

    @PreDestroy
    public void destroy() {
        masterLock = redissonClient.getLock(RedisKey.Lock_ElectionMasterLock);
        log.info("master 节点关闭释放解锁" + lMaster);
        if (lMaster && masterLock != null && masterLock.isHeldByCurrentThread()) {
            masterLock.unlock();
            log.info("master 关锁成功");
        }
    }


}
