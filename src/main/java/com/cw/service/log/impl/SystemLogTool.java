package com.cw.service.log.impl;

import com.cw.utils.CacheLog;
import com.cw.utils.RedisKey;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @Describe
 * <AUTHOR> <PERSON>
 * @Create on 2021/12/21 0021
 */
@Component
public class SystemLogTool extends CacheLog {
    static SystemLogTool logTool;
    @Autowired
    RedisTemplate<String, String> redisTemplate;

    @Autowired
    RabbitTemplate rabbitTemplate;


    public static SystemLogTool getInstance() {
        return logTool;
    }

    @PostConstruct
    public void init() {
        logTool = this;
    }


    /**
     * 用户日志写入redis缓存，定时批量写入数据库
     *
     * @param userLog
     */
    @Override
    public void sendUserCacheLog(String userLog) {
        redisTemplate.opsForList().rightPush(RedisKey.userLogList, userLog);
    }




    //@Override
    //public void writeUserLog(SystemUtil.UserlogType logtype, SystemUtil.UserlogOpType opType, String userid, String regno, String content) {
    //    redisTemplate.opsForList().rightPush(RedisKey.userloglist, JSON.toJSONString(userlog));
    //    //RQueue<String> rQueue= redissonClient.getQueue(RedisKey.userloglist);
    //    //rQueue.add(JSON.toJSONString(userlog));
    //}
}
