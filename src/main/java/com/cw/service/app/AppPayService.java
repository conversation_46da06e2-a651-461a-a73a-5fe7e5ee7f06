package com.cw.service.app;

import com.cw.core.platform.wechat.wxpay.WxNotifyResult;
import com.cw.exception.DefinedException;
import com.cw.pojo.dto.app.req.AppOnlinePayReq;
import com.cw.pojo.dto.app.req.AppOrderPayQueryReq;
import com.cw.pojo.dto.app.res.AppQrScanPayRes;
import com.cw.pojo.dto.app.res.AppQrcodePayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;

import javax.servlet.http.HttpServletRequest;

/**
 * 支付服务
 *
 * <AUTHOR>
 * @Descripstion
 * @Create 2021/11/4 10:54
 **/
public interface AppPayService {


    AppQrScanPayRes createWxScanPayOrder(AppOnlinePayReq req) throws DefinedException;

    AppQrScanPayRes createALiScanPayOrder(AppOnlinePayReq req) throws DefinedException;

    AppQueryPayRes queryScanPayOrder(AppOnlinePayReq req) throws DefinedException;
    /**
     * 获取一个微信二维码支付
     *
     * @param req
     * @return
     * @throws DefinedException
     */
    AppQrcodePayRes createWxNativeOrder(AppOnlinePayReq req) throws DefinedException;


    WxNotifyResult handleWxJsApiOrderPayNotify(String appid, String body, HttpServletRequest request);


    WxNotifyResult handleWxJsApiRefundNotify(String appid, String body, HttpServletRequest request);


    String handleAliPayRefundNotify(String appid, String body, HttpServletRequest request);

    String handleAliPassByRefundNotify(String appid, String body, HttpServletRequest request);


    AppQueryPayRes queryOrderPayStatus(AppOrderPayQueryReq req) throws DefinedException;


    String createAliQrcodePayOrder(AppOnlinePayReq req) throws DefinedException;

    String handlerAliPayNotify(String appid, String body, HttpServletRequest request);


}
