package com.cw.service.config.sequence;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.config.exception.CustomException;
import com.cw.entity.Factor;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_UpdSeq_Req;
import com.cw.service.context.GlobalContext;
import com.cw.service.log.UserLogService;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 公共的表记录排序方法
 *
 * <AUTHOR>
 */
@Service
public class SequenceService {


    private static Map<String, GlobalDataType> seqSupportMap = Maps.newHashMap();

    static {
        seqSupportMap.put(Factor.class.getSimpleName(), GlobalDataType.FACTOR);


    }

    private DaoLocal<?> daoLocal;
    private GlobalCache globalCache;
    @Autowired
    UserLogService userLogService;

    public SequenceService(DaoLocal<?> daoLocal, GlobalCache globalCache) {
        this.daoLocal = daoLocal;
        this.globalCache = globalCache;
    }

    private GlobalDataType getSupportType(Common_UpdSeq_Req req) {
        return seqSupportMap.getOrDefault(req.getTableName(), null);
    }

    /**
     * 获取新的排序字段值
     *
     * @param tableClass
     * @param hotelId
     * @param columnMap
     * @return
     */
    public int getNewSeq(Class<?> tableClass, String hotelId, Map<String, Object> columnMap) {
        //todo 并发下不一定是最新数据
        synchronized (this) {
            int count = 0;
            if (seqSupportMap.containsKey(tableClass.getSimpleName())) {
                //max(seq)为null返回0
                String jpql = StrUtil.format("select coalesce(max(seq), 0) from {} where hotelId='{}' ", tableClass.getSimpleName(), hotelId);
                if (CollectionUtil.isNotEmpty(columnMap)) {
                    for (String columnKey : columnMap.keySet()) {
                        if (columnMap.get(columnKey) instanceof String) {
                            jpql = jpql + StrUtil.format(" and {}='{}' ", columnKey, columnMap.get(columnKey));
                        } else {
                            jpql = jpql + StrUtil.format(" and {}={} ", columnKey, columnMap.get(columnKey));
                        }
                    }

                }
                count = daoLocal.getCountOption(jpql);
            }
            return count + 1;
        }
    }

    /**
     * @param tableClass 需要置顶类的表
     * @param hotelId    项目Id
     * @param id         需要置顶的slqid
     * @param columnMap  条件约束的集合
     * @return
     */
    public void updateTopSeq(Class<?> tableClass, String hotelId, Long id, Map<String, Object> columnMap) {

        if (seqSupportMap.containsKey(tableClass.getSimpleName())) {
            //min(seq)为null返回1
            String jpql = StrUtil.format("select coalesce(min(seq), 1) from {} where hotelId='{}' ", tableClass.getSimpleName(), hotelId);
            //条件约束
            if (CollectionUtil.isNotEmpty(columnMap)) {
                for (String columnKey : columnMap.keySet()) {
                    if (columnMap.get(columnKey) instanceof String) {
                        jpql = jpql + StrUtil.format(" and {}='{}' ", columnKey, columnMap.get(columnKey));
                    } else {
                        jpql = jpql + StrUtil.format(" and {}={} ", columnKey, columnMap.get(columnKey));
                    }
                }

            }
            //获取搜索条件内最小排序  更新最小值数据seq+1，设置当前数据seq最小值
            Integer minSeq = daoLocal.getCountOption(jpql);
            Integer nextSeq = minSeq + 1;
            String updSql1 = "update {} set seq=?1 where seq=?2  and hotelId=?3 ";
            if (CollectionUtil.isNotEmpty(columnMap)) {
                updSql1 = updSql1 + getMapSql(columnMap);

            }
            String updSql2 = "update {} set seq=?1 where id=?2";
            updSql1 = StrUtil.format(updSql1, tableClass.getSimpleName());
            updSql2 = StrUtil.format(updSql2, tableClass.getSimpleName());
            daoLocal.batchOption(updSql1, nextSeq, minSeq, hotelId);
            daoLocal.batchOption(updSql2, minSeq, id);
        }

    }


    public void updTableSeq(Common_UpdSeq_Req seqReq) {
        GlobalDataType dataType = getSupportType(seqReq);
        String hotelId = GlobalContext.getCurrentHotelId();
        if (dataType == null) {
            throw new CustomException(ResultJson.failure(ResultCode.SERVER_ERROR).msg(seqReq.getTableName() + "不支持排序"));
        }

        String seekJpql = StrUtil.format("select  seq from {} where id={} ", seqReq.getTableName(), seqReq.getId());
        Integer orgSeq = daoLocal.getObject(seekJpql);
        if (orgSeq == null) {
            return;
        }
        //有可能有相同序号 如
        String seekTargetJpql = StrUtil.format("select seq from {} where id<>{} and hotelId='{}' and seq {} {}",
                seqReq.getTableName(),
                seqReq.getId(),
                hotelId,
                seqReq.getLup() ? "<=" : ">=",
                orgSeq);

        //添加额外条件
        if (CollectionUtil.isNotEmpty(seqReq.getColumnMap())) {
            seekTargetJpql = seekTargetJpql + getMapSql(seqReq.getColumnMap());

        }

        //判断请求升降排序 选相临近的排序
        String sort = seqReq.getLup() ? "desc" : "asc";
        seekTargetJpql = seekTargetJpql + StrUtil.format("order by seq {}", sort);

        Integer targetSeq = daoLocal.getObject(seekTargetJpql);
        //判断初始排序和模板排序是否一致，一致则不需要更新
        if (orgSeq != null && targetSeq != null) {
            if (!orgSeq.equals(targetSeq)) {
                //如果没有相同排序 互换排序
                String updSql1 = "update {} set seq=?1 where seq=?2  and hotelId=?3 ";
                //添加额外条件
                if (CollectionUtil.isNotEmpty(seqReq.getColumnMap())) {
                    updSql1 = updSql1 + getMapSql(seqReq.getColumnMap());
                    updSql1 = StrUtil.format(updSql1, seqReq.getTableName());
                    //互换排序
                    daoLocal.batchOption(updSql1, orgSeq, targetSeq, hotelId);
                }
            } else {
                //如果有相同排序 判断排序加减
                if (seqReq.getLup()) {
                    targetSeq = targetSeq - 1 <= 0 ? 1 : targetSeq - 1; //上升排序判断边界
                } else {
                    targetSeq = targetSeq + 1;//下降直接+1
                }
            }
            //更新目标排序
            String updSql2 = "update {} set seq=?1 where id=?2";
            updSql2 = StrUtil.format(updSql2, seqReq.getTableName());
            daoLocal.batchOption(updSql2, targetSeq, seqReq.getId());
            ////写入日志
            writeUserLog(seqReq, orgSeq, targetSeq);

        }
    }

    /**
     * 判断更新排序的类型写入操作日志
     *
     * @param seqReq    变更排序信息
     * @param orgSeq    初始排序号
     * @param targetSeq 更新排序号
     */
    private void writeUserLog(Common_UpdSeq_Req seqReq, Integer orgSeq, Integer targetSeq) {
        SystemUtil.UserLogType type = null;
        if (seqReq.getTableName().equalsIgnoreCase(SystemUtil.UserLogType.FACTOR.name())) {
            type = SystemUtil.UserLogType.FACTOR;
        }
        if (type != null) {
            //查询更新的类型
            StringBuilder detailStr = new StringBuilder();
            if (CollectionUtil.isNotEmpty(seqReq.getColumnMap())) {
                for (Map.Entry<String, Object> entry : seqReq.getColumnMap().entrySet()) {
                    detailStr.append(StrUtil.format(" {},", entry.getValue()));
                }
            }
            String content = "变更" + type.getDesc() + detailStr.toString() + "id:" + seqReq.getId() + "排序：" + orgSeq + "=>" + targetSeq;
            userLogService.writeLog(type, SystemUtil.UserLogOpType.MODIFY, GlobalContext.getCurrentUserId(),
                    seqReq.getId() + "", content, GlobalContext.getCurrentHotelId());
        }
    }

    /**
     * map参数拼接，默认拼接字符型参数
     *
     * @param map
     * @return
     */
    private String getMapSql(Map<String, Object> map) {
        String sqlStr = "";
        for (String columnKey : map.keySet()) {
            if (map.get(columnKey) instanceof String) {
                sqlStr = sqlStr + StrUtil.format(" and {}='{}' ", columnKey, map.get(columnKey));
            } else {
                sqlStr = sqlStr + StrUtil.format(" and {}={} ", columnKey, map.get(columnKey));
            }
        }
        return sqlStr;
    }


}
