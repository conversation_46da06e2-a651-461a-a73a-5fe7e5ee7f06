package com.cw.service.config.roomtype.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.config.exception.BizException;
import com.cw.config.exception.CustomException;
import com.cw.core.CoreAvl;
import com.cw.core.SeqNoService;
import com.cw.entity.RoomType;
import com.cw.entity.Rrooms;
import com.cw.mapper.RoomMapper;
import com.cw.mapper.RoomQuantityMapper;
import com.cw.mapper.RoomTypeMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.common.ResultCode;
import com.cw.pojo.common.ResultJson;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.roomtype.QueryRoomTypeReq;
import com.cw.pojo.dto.pms.req.roomtype.RoomTypeEntity;
import com.cw.pojo.dto.pms.req.roomtype.UpdateRoomTypeInfoReq;
import com.cw.pojo.dto.pms.res.roomtype.RoomTypeListRes;
import com.cw.pojo.dto.pms.res.roomtype.RoomTypeRes;
import com.cw.service.config.roomtype.RoomTypeService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.JpaUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.jpa.DynamicSpecificationBuilder;
import com.cw.utils.jpa.Operator;
import com.cw.utils.jpa.SearchCriteria;
import com.cw.utils.pages.PageResUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 房型信息接口实现
 * @Author: michael.pan
 * @Date: 2024/3/18 22:06
 */
@Service
public class RoomTypeServiceImpl implements RoomTypeService {

    @Resource
    private RoomTypeMapper roomTypeMapper;

    @Resource
    private RoomMapper roomMapper;

    @Resource
    private CoreAvl coreAvl;

    @Autowired
    private DaoLocal<?> daoLocal;

    @Override
    public void addRoomType(RoomTypeEntity hotelInfoRequest) {
        String currentHotelId = GlobalContext.getCurrentHotelId();
        String description = hotelInfoRequest.getDescription();
        //校验重复性
        int roomTypeSize = roomTypeMapper.countByHotelIdAndDescription(currentHotelId, description);
        if(roomTypeSize > 0){
            throw new BizException(StrUtil.format("房型为{}已存在，无法重复添加！", description));
        }
        RoomType roomtype = new RoomType();
        // 复制对象
        BeanUtil.copyProperties(hotelInfoRequest, roomtype);
        roomtype.setHotelId(GlobalContext.getCurrentHotelId());
        roomtype.setRoomType(SeqNoService.getInstance().getUniqueCode(description, roomtype.getHotelId(), GlobalDataType.ROOMTYPE));
        roomTypeMapper.save(JpaUtil.appendEntity(roomtype));


        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOMTYPE, currentHotelId);

        try {
            coreAvl.generateRrooms(roomtype.getHotelId(), Arrays.asList(roomtype.getRoomType()), null, null);
        } catch (Exception e) {
            throw new CustomException(ResultJson.failure(ResultCode.FORMERR).msg("房量生成失败.请重新尝试"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRoomType(UpdateRoomTypeInfoReq roomTypeInfoRequest) {
        // 先查询
        Long id = roomTypeInfoRequest.getId();
        Optional<RoomType> data = roomTypeMapper.findById(id);
        if (!data.isPresent()) {
            throw new BizException(StrUtil.format("找不到对应的id为{}的可卖房量数据", id));
        }
        String description = roomTypeInfoRequest.getDescription();
        if (StrUtil.isEmpty(description)) {
            throw new BizException("房型名称不能为空");
        }
        String currentHotelId = GlobalContext.getCurrentHotelId();
        //房型重复性校验
        int roomTypeSize = roomTypeMapper.countByHotelIdAndDescription(currentHotelId, description);
        if (!data.get().getDescription().equalsIgnoreCase(description) && roomTypeSize > 0) {
            //不允许修改成已经存在的房间号
            throw new BizException(StrUtil.format("该房型{}已存在，请确认后重试", description));
        }

        // 更新房型
        roomTypeMapper.save(JpaUtil.appendEntity(data, roomTypeInfoRequest));

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOMTYPE, currentHotelId);
    }

    public RoomTypeEntity load(Common_Load_Req req) {
        Optional<RoomType> data = roomTypeMapper.findById(req.getId());
        if (!data.isPresent()) {
            throw new BizException("数据不存在");
        }
        RoomType roomType = data.get();
        RoomTypeEntity roomTypeEntity = new RoomTypeEntity();
        BeanUtil.copyProperties(roomType, roomTypeEntity, CopyOptions.create().ignoreNullValue());
        return roomTypeEntity;
    }

    ;

    @Override
    public RoomTypeListRes listRoomType(QueryRoomTypeReq queryRoomTypeReq) {
        String hotelId = GlobalContext.getCurrentHotelId();
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("bedType", queryRoomTypeReq.getBedType(), Operator.EQUAL));
        conditionList.add(new SearchCriteria("description", queryRoomTypeReq.getDescription(), Operator.LIKE));
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", hotelId, Operator.EQUAL));
        Specification<RoomType> specification = DynamicSpecificationBuilder.getQuerySpecification(RoomType.class, conditionList);
        Page<RoomType> roomTypes = roomTypeMapper.findAll(specification, JpaUtil.getPageRequest(queryRoomTypeReq.getPages()));
        RoomTypeListRes result = PageResUtil.fillPagesDate(roomTypes, RoomTypeListRes.class);
        //转换床型代码和楼栋代码转换
        if (CollectionUtil.isNotEmpty(result.getRecords())) {
            for (RoomTypeRes node: result.getRecords()) {
                //床型转换中文
                node.setBedType(CustomData.getDesc(hotelId,node.getBedType(), SystemUtil.CustomDataKey.bedtype));
                //楼栋转换描述
                if (StringUtils.isNotBlank(node.getBuildingNo())) {
                    node.setBuildingNo(CustomData.getDesc(hotelId,node.getBuildingNo(), SystemUtil.CustomDataKey.building));
                }
            }

        }
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {


        Optional<RoomType> roomType = roomTypeMapper.findById(id);
        if (!roomType.isPresent()) {
            throw new BizException(StrUtil.format("找不到对应的id为{}的房间数据", id));
        }
        //删除某个房型，则对应可卖房某个房型的数据也将被删除
        String roomTypeCode = roomType.get().getRoomType();
        String currentHotelId = GlobalContext.getCurrentHotelId();
        //校验该房型是否被房间使用
        int roomSize = roomMapper.countByHotelIdAndRoomType(currentHotelId, roomTypeCode);
        if (roomSize > 0) {
            throw new BizException("该房型已使用，请先更改所有相关房间的房型，再尝试删除");
        }
        int count = daoLocal.getCountOption("select count(*) from Reservation where roomType =?1 and hotelId =?2", currentHotelId, roomTypeCode);
        if (count > 0) {
            throw new BizException("该房型已被预订，不能删除");
        }
        //Rrooms rooms = roomQuantityMapper.findByHotelIdAndRoomType(currentHotelId, roomTypeCode);
        //if (ObjectUtils.isNotEmpty(rooms)) {
        //    roomQuantityMapper.deleteById(rooms.getId());
        //}
        roomTypeMapper.deleteById(id);

        daoLocal.batchOption("delete from Rrooms where hotelId =?1 and roomType =?2", currentHotelId, roomTypeCode);

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOMTYPE, currentHotelId);
    }
}
