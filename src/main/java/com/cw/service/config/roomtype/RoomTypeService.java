package com.cw.service.config.roomtype;

import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.roomtype.QueryRoomTypeReq;
import com.cw.pojo.dto.pms.req.roomtype.RoomTypeEntity;
import com.cw.pojo.dto.pms.req.roomtype.UpdateRoomTypeInfoReq;
import com.cw.pojo.dto.pms.res.roomtype.RoomTypeListRes;

/**
 * @Description: 房型信息接口
 * @Author: michael.pan
 * @Date: 2024/3/18 21:31
 */
public interface RoomTypeService {
    void addRoomType(RoomTypeEntity hotelInfoRequest);

    void updateRoomType(UpdateRoomTypeInfoReq hotelInfoRequest);

    RoomTypeEntity load(Common_Load_Req req);

    RoomTypeListRes listRoomType(QueryRoomTypeReq queryRoomTypeReq);

    void delete(Long id);
}
