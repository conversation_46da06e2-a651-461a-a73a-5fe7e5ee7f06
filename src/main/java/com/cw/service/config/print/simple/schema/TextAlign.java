package com.cw.service.config.print.simple.schema;

public enum TextAlign {
	/** */
	LEFT("Left")//
	/** */
	,CENTER("Center")//
	/** */
	,RIGHT("Right")//
	/** */
	,JUSTIFIED("Justified")//
	/**  */ 
	,TOP("Top")//	
	/**  */ 
	,MIDDLE("Middle")//	
	/**  */ 
	,BOTTOM("Bottom")//
	;

	/** */
	private final transient String name;

	private TextAlign(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}
}
