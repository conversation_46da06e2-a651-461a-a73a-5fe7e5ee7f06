<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="GuestBilling" language="java" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="909ecc26-4d81-4476-a05d-76f2ff5d4246">
	<parameter name="HotelName" class="java.lang.String"/>
	<parameter name="RESERVATION.GUESTNAME" class="java.lang.String"/>
	<parameter name="RESERVATION.GROUPNAME" class="java.lang.String"/>
	<parameter name="RESERVATION.RESERVATIONNUMBER" class="java.lang.String"/>
	<parameter name="RESERVATION.ROOMNUMBER" class="java.lang.String"/>
	<parameter name="RESERVATION.ARRIVALDATE" class="java.util.Date"/>
	<parameter name="RESERVATION.DEPARTUREDATE" class="java.util.Date"/>
	<parameter name="FOLIO_NO" class="java.lang.String"/>
	<parameter name="BALANCE" class="java.math.BigDecimal"/>
	<parameter name="CURR_TIME" class="java.util.Date"/>
	<parameter name="TOTAL_CHARGE" class="java.math.BigDecimal"/>
	<parameter name="TOTAL_PAMENT" class="java.math.BigDecimal"/>
	<query language="sql"><![CDATA[]]></query>
	<field name="GUESTACCOUNTS.DESCRIPTION" class="java.lang.String"/>
	<field name="GUESTACCOUNTS.CREATEDATE" class="java.util.Date"/>
	<field name="GUESTACCOUNTS.BUSINESS_DATE" class="java.time.LocalDate"/>
	<field name="REMARK" class="java.lang.String"/>
	<field name="CHARGE" class="java.lang.String"/>
	<field name="PAYMENT" class="java.lang.String"/>
	<background splitType="Stretch"/>
	<title height="2" splitType="Stretch">
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</title>
	<pageHeader height="211" splitType="Stretch">
		<element kind="staticText" uuid="87c036bf-bc6f-47f9-bdf2-60bc81c0d2ce" x="0" y="80" width="120" height="30" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true">
			<text><![CDATA[姓名GuestName]]></text>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="staticText" uuid="1d30bb97-68de-41a1-8f72-a12a98a5d28a" x="0" y="110" width="120" height="30" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true">
			<text><![CDATA[团队Group]]></text>
		</element>
		<element kind="staticText" uuid="e1c3d674-9710-49a2-9973-d7b7b44148b2" x="0" y="140" width="120" height="30" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true">
			<text><![CDATA[订单号Order Number]]></text>
		</element>
		<element kind="staticText" uuid="2fb44336-f7e5-444c-baac-75d8a15b5234" x="0" y="170" width="120" height="30" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true">
			<text><![CDATA[账号Acc No]]></text>
		</element>
		<element kind="staticText" uuid="24c02d18-354a-4c2a-80b5-3f5798c7c0dd" x="280" y="170" width="120" height="30" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true">
			<text><![CDATA[打印时间Printing time]]></text>
		</element>
		<element kind="staticText" uuid="f2f7cf2e-29b1-45d4-aed7-404aa3e60ca6" x="280" y="140" width="120" height="30" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true">
			<text><![CDATA[离店日期Dep Date]]></text>
		</element>
		<element kind="staticText" uuid="9666cd54-d952-4116-935c-d19fece3ab1c" x="280" y="110" width="120" height="30" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true">
			<text><![CDATA[到店日期Arr Date]]></text>
		</element>
		<element kind="staticText" uuid="13004e36-9969-4bf3-9156-26a83fefb14d" x="280" y="80" width="120" height="30" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true">
			<text><![CDATA[房间号码Room NO]]></text>
		</element>
		<element kind="textField" uuid="a1af5baa-89b5-4721-81c9-131f06279962" x="120" y="80" width="150" height="30">
			<expression><![CDATA[$P{RESERVATION.GUESTNAME}]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="2becbcef-bd57-4bde-b8a9-48e20685f569" x="120" y="110" width="150" height="30">
			<expression><![CDATA[$P{RESERVATION.GROUPNAME}]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="bb7af0a6-584f-48d3-8754-5b74fb2a727a" x="120" y="140" width="150" height="30">
			<expression><![CDATA[$P{RESERVATION.RESERVATIONNUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="13c5b49b-7fdd-463d-a020-22d2dcd7e1ba" x="120" y="170" width="150" height="30">
			<expression><![CDATA[$P{FOLIO_NO}]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="48eaa5d8-e2c3-4c88-a32e-b34cbdb5a420" x="400" y="80" width="150" height="30">
			<expression><![CDATA[$P{RESERVATION.ROOMNUMBER}]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="4f72d837-35ff-40f5-8f0a-d240616a0a2f" x="400" y="110" width="150" height="30">
			<expression><![CDATA[$P{RESERVATION.ARRIVALDATE}.toLocaleString()]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="8f9a20c7-11ed-419d-a68e-df1f2970f04e" x="400" y="140" width="150" height="30">
			<expression><![CDATA[$P{RESERVATION.DEPARTUREDATE}.toLocaleString()]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="656a411e-1140-4915-84cf-abd418805a42" x="400" y="170" width="150" height="30">
			<expression><![CDATA[$P{CURR_TIME}]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
		</element>
		<element kind="textField" uuid="cab92d61-c483-4d4e-a909-69d04a3aac31" x="0" y="0" width="550" height="30" fontName="Serif" fontSize="16.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$P{HotelName}]]></expression>
		</element>
		<element kind="staticText" uuid="47ec85cd-b8c3-412f-b885-4064f2323a2d" x="0" y="30" width="550" height="40" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true" hTextAlign="Center" vTextAlign="Middle">
			<text><![CDATA[客人账单
Guest Billing]]></text>
		</element>
	</pageHeader>
	<columnHeader height="25" splitType="Stretch">
		<element kind="staticText" uuid="396af909-9f7a-4b0f-b0d9-f9627960e686" x="0" y="0" width="100" height="20" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true" vTextAlign="Middle">
			<text><![CDATA[日期Date]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="staticText" uuid="d5fcddd3-3236-410f-a8d5-6decd1a6172d" x="100" y="0" width="250" height="20" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true" vTextAlign="Middle">
			<text><![CDATA[摘要Remark]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="staticText" uuid="d715ca1e-04b1-4cb7-b7b4-57a777808dca" x="350" y="0" width="100" height="20" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true" hTextAlign="Right" vTextAlign="Middle">
			<text><![CDATA[消费charge]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="staticText" uuid="b471047f-30fa-4462-9fd9-4d82b9aaa183" x="450" y="0" width="100" height="20" fontSize="12.0" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" bold="true" pdfEmbedded="true" hTextAlign="Right" vTextAlign="Middle">
			<text><![CDATA[付款Payment]]></text>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
	</columnHeader>
	<detail>
		<band height="22" splitType="Stretch">
			<element kind="textField" uuid="0a94598b-dbb6-4f5b-a058-5179246ed03c" x="0" y="0" width="100" height="20">
				<expression><![CDATA[$F{GUESTACCOUNTS.BUSINESS_DATE}]]></expression>
				<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="4467e763-873f-44f7-8fde-e358348aef28"/>
			</element>
			<element kind="textField" uuid="1ceb7a94-f468-4324-a8c6-c7d3869f1544" x="100" y="0" width="250" height="20">
				<expression><![CDATA[$F{REMARK}]]></expression>
				<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="c9460a9e-b645-473a-b2b9-5762d9cbcf0c"/>
			</element>
			<element kind="textField" uuid="6bb1611a-30ed-4bee-9419-8089aa43f715" x="350" y="0" width="100" height="20" hTextAlign="Right">
				<expression><![CDATA[$F{CHARGE}]]></expression>
				<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eb0a9ec2-a994-4658-bd1d-0c5145ef3090"/>
			</element>
			<element kind="textField" uuid="fa31d2a7-**************-19b925ba8734" x="450" y="0" width="100" height="20" hTextAlign="Right">
				<expression><![CDATA[$F{PAYMENT}]]></expression>
				<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="d94cd64d-b2e9-45c6-9ee4-5cdc50584882"/>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<columnFooter height="2" splitType="Stretch">
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</columnFooter>
	<pageFooter height="22" splitType="Stretch">
		<element kind="textField" uuid="80f07b3c-9627-49da-a47c-11979e327d4d" x="0" y="0" width="547" height="20" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA[$V{PAGE_COUNT} +" / "+ $V{REPORT_COUNT}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
	<lastPageFooter height="157" splitType="Stretch">
		<element kind="staticText" uuid="20cbd8ac-9db6-4dd2-b248-d409b09fa709" positionType="FixRelativeToBottom" x="2" y="5" width="550" height="100" fontSize="14.0">
			<text><![CDATA[本人愿承担账单最后所列尚未清付之欠款。
I agree that my liability for his bill is not waived and agree to be personally liable in the event that the indicatedperson, company or association fails to pay for any part or the full amount of the charge.]]></text>
		</element>
		<element kind="staticText" uuid="f7f45f03-3342-46ff-bea3-e8663e7d7e0f" positionType="FixRelativeToBottom" x="422" y="115" width="130" height="42" fontSize="12.0" hTextAlign="Center">
			<text><![CDATA[客人签署
Guest Signature]]></text>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</lastPageFooter>
	<summary height="2" splitType="Stretch">
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</summary>
</jasperReport>
