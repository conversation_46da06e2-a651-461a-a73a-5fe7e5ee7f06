package com.cw.service.config.print.report.service;

import java.io.FileOutputStream;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.cw.report.enums.ExportType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cw.service.config.print.report.parameter.AbstractReportParameter;

@Service
public class ReportServiceManager {
	private final Map<String, ReportService<?>> reportServices;

	@Autowired
	public ReportServiceManager(List<ReportService<?>> services) {
		this.reportServices = services.stream().collect(Collectors.toMap(ReportService::getReportName, Function.identity()));
	}

	public ReportService<?> getReportService(String reportName) {
		return reportServices.get(reportName);
	}

	public Map<String, List<ParameterInfo>> getAllParametersInfo() {
		return reportServices.values().stream()
				.collect(
						Collectors
								.toMap(ReportService::getReportName,
										service -> service.getSupportedParameters().stream()
												.map(p -> new ParameterInfo(p.getParameterName(), p.getParameterType().getSimpleName(),
														p instanceof AbstractReportParameter ? ((AbstractReportParameter<?>) p).getDescription() : ""))
												.collect(Collectors.toList())));
	}

	public static class ParameterInfo {
		public final String name;
		public final String type;
		public final String description;

		public ParameterInfo(String name, String type, String description) {
			this.name = name;
			this.type = type;
			this.description = description;
		}
	}

//try (FileOutputStream outputStream = new FileOutputStream("MultiLevelGroupingReport.pdf")) {
//   ReportService<?> service = reportServiceManager.getReportService("DailyAuditChecklist");
//   service.generateReport(ExportType.PDF, outputStream, service.parseParameters(Map.of("fromDate", "2021-01-01", "toDate", "2026-01-01", "cashierId", "ADMIN")));
//   outputStream.close();
//  }
}
