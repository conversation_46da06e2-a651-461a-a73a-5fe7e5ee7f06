package com.cw.service.config.ota.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.SysFunLibTool;
import com.cw.cache.GlobalCache;
import com.cw.cache.OptionSwitchTool;
import com.cw.cache.impl.*;
import com.cw.config.exception.BizException;
import com.cw.core.CoreOtaIfc;
import com.cw.core.CoreRs;
import com.cw.core.func.order.OtaReqConverter;
import com.cw.core.func.order.req.OtaColRsUpd;

import com.cw.entity.AccountItem;
import com.cw.entity.*;
import com.cw.exception.DefinedException;
import com.cw.mapper.RoomQuantityMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.pms.model.*;
import com.cw.pms.request.*;
import com.cw.pms.request.crsv1.CwCrsColRsSaveV1Req;
import com.cw.pms.request.crsv1.CwCrsRoomRsSaveV1Req;
import com.cw.pms.response.*;
import com.cw.pms.response.crsv1.CwCrsColRsSaveV1Res;
import com.cw.pms.response.crsv1.CwCrsRoomSaveV1Res;
import com.cw.pojo.dto.pms.req.cashier.CashierAccountReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostResp;
import com.cw.pojo.dto.pms.req.reservation.Accompany;
import com.cw.pojo.dto.pms.req.reservation.BatchAssignRoomReq;
import com.cw.pojo.dto.pms.res.reservation.RsOrderResult;
import com.cw.pojo.dto.pms.res.room.DailyRoomTypeData;
import com.cw.pojo.sqlresult.RroomsAvlPo;
import com.cw.service.config.cashier.CashierService;
import com.cw.service.config.ota.PmsOtaApiService;
import com.cw.service.context.OtaGlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.IdCardTypeEnum;
import com.cw.utils.options.Options;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PMS系统OTA接口服务实现类
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/2/19 00:48
 **/
@Service
public class PmsOtaApiServiceImpl implements PmsOtaApiService {

    @Autowired
    RoomQuantityMapper roomQuantityMapper;

    @Autowired
    CoreRs coreRs;

    @Autowired
    CoreOtaIfc coreOtaIfc;

    @Autowired
    CashierService cashierService;

    @Autowired
    DaoLocal<?> daoLocal;

    /**
     * 查询区域信息.
     *
     * @param req 区域查询请求参数
     * @return 区域信息响应
     */
    @Override
    public CwAreaQueryRes queryArea(CwAreaQueryReq req) {
        CwAreaQueryRes res = new CwAreaQueryRes();
        CwAreaQueryRes.BizModel bizModel = new CwAreaQueryRes.BizModel();
        bizModel.setList(Lists.newArrayList());
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询block 信息
     *
     * @param req 区块配额查询请求参数
     * @return 区块配额信息响应
     */
    @Override
    public CwBlockAllotmentQueryRes queryBlockAllotment(CwBlockAllotmentQueryReq req) {
        CwBlockAllotmentQueryRes res = new CwBlockAllotmentQueryRes();
        CwBlockAllotmentQueryRes.BizModel bizModel = new CwBlockAllotmentQueryRes.BizModel();
        bizModel.setList(Lists.newArrayList());
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询渠道信息
     * @param req 渠道查询请求参数
     * @return 渠道信息响应，包含渠道代码和描述
     */
    @Override
    public CwChannelQueryRes queryChannel(CwChannelQueryReq req) {
        CwChannelQueryRes res = new CwChannelQueryRes();
        CwChannelQueryRes.BizModel bizModel = new CwChannelQueryRes.BizModel();

        String hotelId = OtaGlobalContext.getCurrentHotelId();
        ChannelCache channelCache = GlobalCache.getDataStructure().getCache(GlobalDataType.CHANNEL);
        List<Channel> cacheDataList = channelCache.getDataList(hotelId);
        List<BaseCode> list = cacheDataList.stream().map(channel -> {
            BaseCode baseCode = new BaseCode();
            baseCode.setCode(channel.getCode());
            baseCode.setDescription(channel.getDescription());
            return baseCode;
        }).collect(Collectors.toList());

        bizModel.setList(list);
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询国家代码信息
     * @param req 国家代码查询请求参数
     * @return 国家代码信息响应
     */
    @Override
    public CwCountryCodeRes queryCountryCode(CwCountryCodeReq req) {
        CwCountryCodeRes res = new CwCountryCodeRes();
        CwCountryCodeRes.BizModel bizModel = new CwCountryCodeRes.BizModel();
        FactorCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.FACTOR);
        List<Factor> factorList= cache.getDataList(OtaGlobalContext.getCurrentHotelId()).stream()
                .filter(f -> SystemUtil.FactoryType.COUNTRY.name().equals(f.getType()))
                .collect(Collectors.toList());

        List<BaseCode> list = factorList.stream().map(factor -> {
            BaseCode baseCode = new BaseCode();
            baseCode.setCode(factor.getCode());
            baseCode.setDescription(factor.getDescription());
            return baseCode;
        }).collect(Collectors.toList());
        bizModel.setData(list);
        res.setData(bizModel);
        return res;
    }

    /**
     * 账项代码查询
     * @param req 部门查询请求参数，type=P表示查询付款账项，否则查询消费账项
     * @return 部门信息响应，包含部门代码和描述
     */
    @Override
    public CwDepartmentQueryRes queryDepartment(CwDepartmentQueryReq req) {
        CwDepartmentQueryRes res = new CwDepartmentQueryRes();
        CwDepartmentQueryRes.BizModel bizModel = new CwDepartmentQueryRes.BizModel();

        String hotelId = OtaGlobalContext.getCurrentHotelId();
        AccountItemCache accountItemCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ACCOUNTITEM);

        //看是查付款账项还是消费账项.通过compareTo来判断
        List<AccountItem> cacheDataList = accountItemCache.getDataList(hotelId).stream()
                .filter(accountItem ->
                        req.getType().equals("P") ? accountItem.getCode().compareTo(SystemUtil.CASH_DEPTCODE) >= 0 : accountItem.getCode().compareTo(SystemUtil.CASH_DEPTCODE) < 0)
                .collect(Collectors.toList());

        List<DepartCode> list = cacheDataList.stream().map(accountItem -> {
            DepartCode baseCode = new DepartCode();
            baseCode.setCode(accountItem.getCode());
            baseCode.setDescription(accountItem.getDescription());
            return baseCode;
        }).collect(Collectors.toList());


        bizModel.setList(list);
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询账户信息
     * @param req 账户查询请求参数
     * @return 账户信息响应
     */
    @Override
    public CwAccountQueryRes queryAccount(CwAccountQueryReq req) {
        CwAccountQueryRes res = new CwAccountQueryRes();
        CwAccountQueryRes.BizModel bizModel = new CwAccountQueryRes.BizModel();

        AccountInfo accountInfo = new AccountInfo();
        bizModel.setAccountInfo(accountInfo);
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询酒店楼栋代码信息
     * @param req 酒店楼栋代码查询请求参数
     * @return 酒店楼栋代码信息响应，包含楼栋代码和描述
     */
    @Override
    public CwHotelCodeQueryRes queryHotelCode(CwHotelCodeQueryReq req) {
        CwHotelCodeQueryRes res = new CwHotelCodeQueryRes();
        CwHotelCodeQueryRes.BizModel bizModel = new CwHotelCodeQueryRes.BizModel();

        BuildingCache buildingCache = GlobalCache.getDataStructure().getCache(GlobalDataType.BUILDING);
        List<Building> cacheDataList = buildingCache.getDataList(OtaGlobalContext.getCurrentHotelId());
        List<BaseCode> list = cacheDataList.stream().map(building -> {
            BaseCode baseCode = new BaseCode();
            baseCode.setCode(building.getCode());
            baseCode.setDescription(building.getDescription());
            return baseCode;
        }).collect(Collectors.toList());

        bizModel.setList(list);
        res.setData(bizModel);
        return res;
    }


    /**
     * 查询证件类型信息
     * @param req 证件类型查询请求参数
     * @return 证件类型信息响应，包含证件类型代码和描述
     */
    @Override
    public CwIDTypeQueryRes queryIDType(CwIDTypeQueryReq req) {
        CwIDTypeQueryRes res = new CwIDTypeQueryRes();
        CwIDTypeQueryRes.BizModel bizModel = new CwIDTypeQueryRes.BizModel();
        //StatusTypeUtils.IdCardType[] idCardTypes = StatusTypeUtils.IdCardType.values();

        IdCardTypeEnum[] types = IdCardTypeEnum.values();
        List<BaseCode> list = Arrays.stream(types).map(idCardType -> {
            BaseCode baseCode = new BaseCode();
            baseCode.setCode(idCardType.getCode() + "");
            baseCode.setDescription(idCardType.getDesc());
            return baseCode;
        }).collect(Collectors.toList());

        bizModel.setList(list);
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询支付方式信息
     * @param req 支付方式查询请求参数
     * @return 支付方式信息响应，包含支付代码、描述和部门代码
     */
    @Override
    public CwPaymentQueryRes queryPayment(CwPaymentQueryReq req) {
        CwPaymentQueryRes res = new CwPaymentQueryRes();
        CwPaymentQueryRes.BizModel bizModel = new CwPaymentQueryRes.BizModel();


        PaymentCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.PAYMENT);
        List<Payment> cacheDataList = cache.getDataList(OtaGlobalContext.getCurrentHotelId());
        List<PaymentInfoNode> list = cacheDataList.stream().map(payment -> {
            PaymentInfoNode node = new PaymentInfoNode();
            node.setCode(payment.getPayCode());
            node.setDescription(payment.getDescription());
            node.setDeptCode(payment.getDepartmentCode());

            return node;
        }).collect(Collectors.toList());

        bizModel.setList(list);
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询预订类型信息
     * @param req 预订类型查询请求参数
     * @return 预订类型信息响应
     */
    @Override
    public CwResTypeQueryRes queryResType(CwResTypeQueryReq req) {
        CwResTypeQueryRes res = new CwResTypeQueryRes();
        CwResTypeQueryRes.BizModel bizModel = new CwResTypeQueryRes.BizModel();
        RsTypeCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.RSTYPE);
        List<ReservationType> data = cache.getDataList(OtaGlobalContext.getCurrentHotelId());
        List<BaseCode> list = data.stream().map(rsType -> {
            BaseCode node = new BaseCode();
            node.setCode(rsType.getCode());
            node.setDescription(rsType.getDescription());
            return node;
        }).collect(Collectors.toList());
        bizModel.setList(list);
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询房型信息
     * @param req 房型查询请求参数，可按楼栋号筛选
     * @return 房型信息响应，包含房型代码、描述、房间数量、床型和最大入住人数
     */
    @Override
    public CwRoomTypeRes queryRoomType(CwRoomTypeReq req) {
        CwRoomTypeRes res = new CwRoomTypeRes();
        CwRoomTypeRes.BizModel bizModel = new CwRoomTypeRes.BizModel();

        RoomTypeCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);

        List<RoomType> data = cache.getDataList(OtaGlobalContext.getCurrentHotelId());
        if (StrUtil.isNotBlank(req.getBuildingNo())) {
            data = data.stream().filter(r -> req.getBuildingNo().equals(r.getBuildingNo())).collect(Collectors.toList());
        }

        List<RoomTypeInfoNode> list = data.stream().map(rmt -> {
            RoomTypeInfoNode node = new RoomTypeInfoNode();
            node.setCode(rmt.getRoomType());
            node.setDescription(rmt.getDescription());
            node.setNum(rmt.getRoomNumber());
            node.setBedType(rmt.getBedType());
            node.setMaxPersons(rmt.getPeopleNumber());
            node.setBuildingNo(rmt.getBuildingNo());
            return node;
        }).collect(Collectors.toList());

        bizModel.setList(list);
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询房间库存信息
     * @param req 房间库存查询请求参数，包含开始日期、结束日期和房型
     * @return 房间库存信息响应，包含每个房型在指定日期范围内的可用房量、超售房量和总房量
     */
    @Override
    public CwRoomInventoryRes queryRoomInventory(CwRoomInventoryReq req) {
        CwRoomInventoryRes res = new CwRoomInventoryRes();
        CwRoomInventoryRes.BizModel bizModel = new CwRoomInventoryRes.BizModel();
        //其实就是查远期房态
        String hotelId = OtaGlobalContext.getCurrentHotelId();


        Date startdate = CalculateDate.stringToDate(req.getStartDate());
        Date enddate = CalculateDate.stringToDate(req.getEndDate());
        Date[] dates = SysFunLibTool.getQueryProperRange(startdate, enddate);
        startdate = dates[0];
        enddate = dates[1];

        List<RroomsAvlPo> avlPoList = roomQuantityMapper.queryRroomsScheduleList(hotelId, startdate, enddate, Arrays.asList(req.getRoomTypes().split(",")));

        Table<String, String, RroomsAvlPo> dbTable = HashBasedTable.create();//创建一个二维表 对应前端数据
        TreeMap<String, DailyRoomTypeData> sumMap = Maps.newTreeMap();

        for (RroomsAvlPo avlPo : avlPoList) {
            dbTable.put(avlPo.getRoomType(), CalculateDate.dateToString(avlPo.getDatum()), avlPo);
        }

        List<RoomInventoryDetails> list = new ArrayList<>();

        // 遍历二维表，将数据转换为前端需要的格式
        for (String roomType : dbTable.rowKeySet()) {
            RoomInventoryDetails details = new RoomInventoryDetails();
            details.setRoomType(roomType);
            details.setDetails(new ArrayList<>());

            for (String date : dbTable.columnKeySet()) {
                RroomsAvlPo avlPo = dbTable.get(roomType, date);
                if (avlPo != null) {
                    // 处理avlPo数据    
                    RoomDetail detail = new RoomDetail();
                    detail.setDate(date);
                    detail.setAvl(avlPo.getAvl());
                    detail.setOverbook(0);
                    detail.setTotal(avlPo.getTotal());
                    details.getDetails().add(detail);
                }
            }
            list.add(details);
        }

        bizModel.setList(list);
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询市场信息
     * @param req 市场查询请求参数
     * @return 市场信息响应
     */
    @Override
    public CwMarketQueryRes queryMarket(CwMarketQueryReq req) {
        CwMarketQueryRes res = new CwMarketQueryRes();
        CwMarketQueryRes.BizModel bizModel = new CwMarketQueryRes.BizModel();
        MarketCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.MARKET);
        List<Market> data = cache.getDataList(OtaGlobalContext.getCurrentHotelId());
        List<BaseCode> list = data.stream().map(rsType -> {
            BaseCode node = new BaseCode();
            node.setCode(rsType.getCode());
            node.setDescription(rsType.getDescription());
            return node;
        }).collect(Collectors.toList());
        bizModel.setList(list);
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询来源信息
     * @param req 来源查询请求参数
     * @return 来源信息响应
     */
    @Override
    public CwSourceQueryRes querySource(CwSourceQueryReq req) {
        CwSourceQueryRes res = new CwSourceQueryRes();
        CwSourceQueryRes.BizModel bizModel = new CwSourceQueryRes.BizModel();
        // RsTypeCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.RSTYPE);
        // List<Source> data = cache.getDataList(OtaGlobalContext.getCurrentHotelId());
        // List<BaseCode> list = data.stream().map(rsType -> {
        //     BaseCode node = new BaseCode();
        //     node.setCode(rsType.getCode());
        //     node.setDescription(rsType.getDescription());
        //     return node;
        // }).collect(Collectors.toList());
        bizModel.setList(new ArrayList<>());
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询应收账户信息
     * @param req 应收账户查询请求参数
     * @return 应收账户信息响应
     */
    @Override
    public CwQueryArAccountRes queryArAccount(CwQueryArAccountReq req) {
        CwQueryArAccountRes res = new CwQueryArAccountRes();
        CwQueryArAccountRes.BizModel bizModel = new CwQueryArAccountRes.BizModel();
        bizModel.setNodes(Lists.newArrayList());
        res.setData(bizModel);
        return res;
    }

    /**
     * 查询房间订单信息
     * @param req 房间订单查询请求参数
     * @return 房间订单信息响应
     */
    @Override
    public CwQueryRoomOrderRes queryRoomOrder(CwQueryRoomOrderReq req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();
        CwQueryRoomOrderRes res = new CwQueryRoomOrderRes();
        CwQueryRoomOrderRes.BizModel bizModel = new CwQueryRoomOrderRes.BizModel();

        List<Reservation> rooms = daoLocal.getObjectListWithLimit("from Reservation where hotelId = ?1 and reservationStatus = 1 and roomNumber =?2 ", 5, hotelId, req.getRoomNo());
        if (!CollectionUtil.isEmpty(rooms)) {
            List<RoomOrderInfoNode> nodes = rooms.stream().map(room -> {
                RoomOrderInfoNode node = new RoomOrderInfoNode();
                node.setRoomOrderId(room.getReservationNumber());
                node.setRoomNo(room.getRoomNumber());
                //List<Accompany> accompanyList = JSON.parseArray(room.getAccompany(), Accompany.class);
                //if (CollectionUtil.isNotEmpty(accompanyList)) {
                //    node.getGuestNames().addAll(accompanyList.stream().map(Accompany::getGuestName).collect(Collectors.toList()));
                //}
                return node;
            }).collect(Collectors.toList());
            bizModel.setRooms(nodes);
        } else {
            bizModel.setRooms(Lists.newArrayList());
        }
        res.setData(bizModel);
        return res;
    }

    /**
     * 分配区块配额
     * @param req 区块配额分配请求参数
     * @return 区块配额分配响应
     */
    @Override
    public CwBlockAllotmentRes allotmentBlock(CwBlockAllotmentReq req) {
        CwBlockAllotmentRes res = new CwBlockAllotmentRes();
        CwBlockAllotmentRes.BizModel bizModel = new CwBlockAllotmentRes.BizModel();
        bizModel.setList(Lists.newArrayList());
        res.setData(bizModel);
        return res;
    }

    /**
     * 保存区块信息
     * @param req 区块保存请求参数
     * @return 区块保存响应，包含区块ID
     */
    @Override
    public CwBlockRes saveBlock(CwBlockReq req) {
        CwBlockRes res = new CwBlockRes();
        CwBlockRes.BizModel bizModel = new CwBlockRes.BizModel();
        bizModel.setBlockid("");
        res.setData(bizModel);
        return res;
    }

    /**
     * 取消房间预订
     * @param req 房间取消请求参数
     * @return 房间取消响应
     */
    @Override
    public CwCancelRoomRes cancelRoom(CwCancelRoomReq req) {

        try {
            coreRs.cancelOrder(OtaGlobalContext.getCurrentHotelId(), req.getPmsRoomId());
        } catch (DefinedException e) {
            throw new RuntimeException(e);
        }

        CwCancelRoomRes res = new CwCancelRoomRes();
        CwCancelRoomRes.BizModel bizModel = new CwCancelRoomRes.BizModel();
        bizModel.setMsg("");
        res.setData(bizModel);

        return res;
    }

    /**
     * 新建或者修改客房预订
     * @param req 房间预订保存请求参数
     * @return 房间预订保存响应
     */
    @Override
    public CwSaveRoomRes saveRoom(CwSaveRoomReq req) {
        CwSaveRoomRes res = new CwSaveRoomRes();
        CwSaveRoomRes.BizModel bizModel = new CwSaveRoomRes.BizModel();
        RoomSaveResult roomSaveResult = new RoomSaveResult();
        bizModel.setData(roomSaveResult);
        res.setData(bizModel);

        //新建OR 创建客房预订
        return res;
    }

    /**
     * 新建或者修改综合预订
     * @param req 团队预订保存请求参数
     * @return 团队预订保存响应，包含预订ID
     */
    @Override
    public CwColRsRes saveColReservation(CwColRsReq req) {
        OtaColRsUpd colRsUpd = new OtaColRsUpd(req);//使用适配器模式 封装一层
        RsOrderResult orderResult = null;
        try {
            orderResult = coreRs.createBatchOrder(colRsUpd);
        } catch (DefinedException e) {
            throw new BizException(e.getMessage());
        }

        if (OptionSwitchTool.getOptionSatus(Options.AUTO_ASSROOM, colRsUpd.getHotelId())) {//TODO 北海民宿需要马上分房
            BatchAssignRoomReq roomReq = new BatchAssignRoomReq();
            roomReq.setRegNos(Lists.newArrayList(orderResult.getReservaitonNumber()));
            coreRs.batchAssignRoomReq(roomReq, colRsUpd.getHotelId());//优先自动分房
        }


        CwColRsRes res = new CwColRsRes();
        CwColRsRes.BizModel bizModel = new CwColRsRes.BizModel();
        //编辑或者修改综合预订
        IdResult idResult = new IdResult();

        bizModel.setIdResult(idResult);
        res.setData(bizModel);
        return res;
    }

    /**
     * 取消综合预订
     * @param req 预订取消请求参数
     * @return 预订取消响应
     */
    @Override
    public CwPmsCommonRes cancelReservation(CwCancelColRsReq req) {
        //TODO 写一个取消综合预订的方法
        Reservation rs = coreRs.seekColPostRs(req.getNetworkId(), "", OtaGlobalContext.getCurrentHotelId());//查找主账房

        if (rs == null) {
            throw new BizException("该预订不存在");
        }
        //判断账务是否平.如果不平.

        try {
            coreRs.cancelOrder(OtaGlobalContext.getCurrentHotelId(), rs.getReservationNumber());
        } catch (DefinedException e) {
            throw new RuntimeException(e);
        }


        CwPmsCommonRes res = new CwPmsCommonRes();
        CwPmsCommonRes.BizModel bizModel = new CwPmsCommonRes.BizModel();
        bizModel.setMsg("");
        res.setData(bizModel);
        return res;
    }

    /**
     * 应收账款支付
     * @param req 应收账款支付请求参数
     * @return 应收账款支付响应
     */
    @Override
    public CwPmsCommonRes arpay(CwOrderArPayReq req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();
        req.setItfNo(OtaGlobalContext.getOtaHeaderValue(OtaGlobalContext.OtaHeader.APPID) + "@" + req.getItfNo());
        CashierPostReq cashierPostReq = OtaReqConverter.convertArPayReq(req);
        String res_no = cashierPostReq.getReservationNumber();
        cashierPostReq.setReservationNumber("");
        CashierPostResp postResp = cashierService.post(hotelId, cashierPostReq, g -> {
            g.setRes_no_org(res_no);
        });

        CwPmsCommonRes res = new CwPmsCommonRes();
        CwPmsCommonRes.BizModel bizModel = new CwPmsCommonRes.BizModel();
        bizModel.setMsg("操作成功");
        res.setData(bizModel);
        return res;
    }

    /**
     * 挂账请求
     * @param req 挂账请求参数
     * @return 挂账请求响应
     *
     */
    @Override
    public CwOrderConsumptionRes roomConsumption(CwOrderConsumptionReq req) {
        CashierPostResp resp = cashierService.post(OtaGlobalContext.getCurrentHotelId(),
                OtaReqConverter.convertConsumptionReq(req));

        CwOrderConsumptionRes res = new CwOrderConsumptionRes();
        CwOrderConsumptionRes.BizModel bizModel = new CwOrderConsumptionRes.BizModel();
        AccountNode accountNode = new AccountNode();
        if (CollectionUtil.isNotEmpty(resp.getAcc_ids())) {
            accountNode.setAccid(resp.getAcc_ids().stream().findFirst().get());
        }
        bizModel.setAccountNode(accountNode);
        res.setData(bizModel);
        return res;
    }

    /**
     * 订单支付
     * @param req 订单支付请求参数
     * @return 订单支付响应
     */
    @Override
    public CwColPayRes orderPay(CwColPayReq req) {
        //TODO 查找该综合预订的主账房预订.就是按预订号.预抵.并且有钱来排序
        String hotelId = OtaGlobalContext.getCurrentHotelId();

        Reservation rs = coreRs.seekColPostRs(req.getNetworkId(), "", hotelId);//查找主账房

        //PaymentCache paymentCache=GlobalCache.getDataStructure().getCache(GlobalDataType.PAYMENT);
        //Payment payment=paymentCache.getRecord(hotelId,req.getPaymentCode());

        CashierPostReq postReq = new CashierPostReq();
        postReq.setReservationNumber(rs.getReservationNumber());//账务要入到客房
        postReq.setRemark("在线支付");

        postReq.getAccounts().add(new CashierAccountReq()
                .setDepartmentCode(req.getPaymentCode())
                .setPrice(BigDecimal.valueOf(req.getAmount().doubleValue())));

        CwColPayRes res = new CwColPayRes();
        CwColPayRes.BizModel bizModel = new CwColPayRes.BizModel();
        res.setData(bizModel);
        return res;
    }

    /**
     * 取消收费
     * @param req 收费取消请求参数
     * @return 收费取消响应
     */
    @Override
    public CwPmsCommonRes cancelCharge(CwCancelChargeReq req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();
        CashierPostResp resp = cashierService.refund(hotelId, req.getAccid(), null, req.getRemark());

        CwPmsCommonRes res = new CwPmsCommonRes();
        CwPmsCommonRes.BizModel bizModel = new CwPmsCommonRes.BizModel();
        bizModel.setMsg("操作成功");
        res.setData(bizModel);
        return res;
    }

    @Override
    public CwQueryColRes queryColRs(CwQueryColReq req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();

        List<Reservation> rooms = daoLocal.getObjectListWithLimit("from Reservation where hotelId = ?1 and otano=?2 ",
                5, hotelId, req.getOtaorderId());

        List<String> rmNos = rooms.stream().map(Reservation::getRoomNumber).filter(s -> Strings.isNotBlank(s)).collect(Collectors.toList());

        CwQueryColRes res = new CwQueryColRes();
        CwQueryColRes.BizModel bizModel = new CwQueryColRes.BizModel();
        bizModel.setRoomNo(rmNos);
        res.setData(bizModel);

        return res;
    }

    @Override
    public CwCheckInRes checkin(CwCheckInReq req) {
        return coreOtaIfc.checkin(req);
    }

    @Override
    public CwQueryCheckinOrderRes queryCheckinOrder(CwQueryCheckinOrderReq req) {
        return coreOtaIfc.queryCheckinOrder(req);
    }

    @Override
    public CwQueryHotelInfoRes queryHotelInfo(CwQueryHotelInfoReq req) {
        String hotelId = OtaGlobalContext.getCurrentHotelId();
        HotelCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.HOTEL);
        Hotel hotel = cache.getRecord(hotelId, hotelId);
        CwQueryHotelInfoRes res = new CwQueryHotelInfoRes();
        CwQueryHotelInfoRes.BizModel bizModel = new CwQueryHotelInfoRes.BizModel();
        bizModel.setHotelName(hotel.getHotelName());
        bizModel.setPhone(hotel.getTelephone());
        bizModel.setHotelAddress(hotel.getAddress());
        res.setData(bizModel);
        return res;
    }

    /**
     * DSPMS综合预订
     * 支持客房预订、餐饮预订、门票预订、导游预订等多种预订类型
     *
     * @param req DSPMS综合预订请求参数
     * @return DSPMS综合预订响应
     */
    @Override
    public CwCrsColRsSaveV1Res saveCwCrsColReservation(CwCrsColRsSaveV1Req req) {

        return null;
    }

    /**
     * DSPMS客房预订
     * 专门用于客房预订的简化接口
     *
     * @param req DSPMS客房预订请求参数
     * @return DSPMS客房预订响应
     */
    @Override
    public CwCrsRoomSaveV1Res saveCwCrsRoom(CwCrsRoomRsSaveV1Req req) {

        return null;
    }


}
