package com.cw.service.config.ota;

import com.cw.pms.request.*;
import com.cw.pms.request.crsv1.CwCrsColRsSaveV1Req;
import com.cw.pms.request.crsv1.CwCrsRoomRsSaveV1Req;
import com.cw.pms.response.*;
import com.cw.pms.response.crsv1.CwCrsColRsSaveV1Res;
import com.cw.pms.response.crsv1.CwCrsRoomSaveV1Res;

/**
 * PMS系统OTA接口服务
 * <AUTHOR>
 * @Descripstion
 * @Create 2025/2/19 00:48
 **/
public interface PmsOtaApiService {
    // Query APIs

    /**
     * 查询区域信息
     *
     * @param req 区域查询请求参数
     * @return 区域信息响应
     */
    CwAreaQueryRes queryArea(CwAreaQueryReq req);

    /**
     * 查询区块配额信息
     * @param req 区块配额查询请求参数
     * @return 区块配额信息响应
     */
    CwBlockAllotmentQueryRes queryBlockAllotment(CwBlockAllotmentQueryReq req);

    /**
     * 查询渠道信息
     * @param req 渠道查询请求参数
     * @return 渠道信息响应
     */
    CwChannelQueryRes queryChannel(CwChannelQueryReq req);

    /**
     * 查询国家代码信息
     * @param req 国家代码查询请求参数
     * @return 国家代码信息响应
     */
    CwCountryCodeRes queryCountryCode(CwCountryCodeReq req);

    /**
     * 查询部门信息
     * @param req 部门查询请求参数
     * @return 部门信息响应
     */
    CwDepartmentQueryRes queryDepartment(CwDepartmentQueryReq req);

    /**
     * 查询账户信息
     * @param req 账户查询请求参数
     * @return 账户信息响应
     */
    CwAccountQueryRes queryAccount(CwAccountQueryReq req);

    /**
     * 查询酒店代码信息
     * @param req 酒店代码查询请求参数
     * @return 酒店代码信息响应
     */
    CwHotelCodeQueryRes queryHotelCode(CwHotelCodeQueryReq req);

    /**
     * 查询证件类型信息
     * @param req 证件类型查询请求参数
     * @return 证件类型信息响应
     */
    CwIDTypeQueryRes queryIDType(CwIDTypeQueryReq req);

    /**
     * 查询支付方式信息
     * @param req 支付方式查询请求参数
     * @return 支付方式信息响应
     */
    CwPaymentQueryRes queryPayment(CwPaymentQueryReq req);

    /**
     * 查询预订类型信息
     * @param req 预订类型查询请求参数
     * @return 预订类型信息响应
     */
    CwResTypeQueryRes queryResType(CwResTypeQueryReq req);

    /**
     * 查询房型信息
     * @param req 房型查询请求参数
     * @return 房型信息响应
     */
    CwRoomTypeRes queryRoomType(CwRoomTypeReq req);

    /**
     * 查询房间库存信息
     * @param req 房间库存查询请求参数
     * @return 房间库存信息响应
     */
    CwRoomInventoryRes queryRoomInventory(CwRoomInventoryReq req);

    /**
     * 查询市场信息
     * @param req 市场查询请求参数
     * @return 市场信息响应
     */
    CwMarketQueryRes queryMarket(CwMarketQueryReq req);

    /**
     * 查询来源信息
     * @param req 来源查询请求参数
     * @return 来源信息响应
     */
    CwSourceQueryRes querySource(CwSourceQueryReq req);

    /**
     * 查询应收账户信息
     * @param req 应收账户查询请求参数
     * @return 应收账户信息响应
     */
    CwQueryArAccountRes queryArAccount(CwQueryArAccountReq req);

    /**
     * 查询房间订单信息
     * @param req 房间订单查询请求参数
     * @return 房间订单信息响应
     */
    CwQueryRoomOrderRes queryRoomOrder(CwQueryRoomOrderReq req);

    // Block APIs

    /**
     * 分配区块配额
     * @param req 区块配额分配请求参数
     * @return 区块配额分配响应
     */
    CwBlockAllotmentRes allotmentBlock(CwBlockAllotmentReq req);

    /**
     * 保存区块信息
     * @param req 区块保存请求参数
     * @return 区块保存响应
     */
    CwBlockRes saveBlock(CwBlockReq req);

    // Order APIs

    /**
     * 取消房间预订
     * @param req 房间取消请求参数
     * @return 房间取消响应
     */
    CwCancelRoomRes cancelRoom(CwCancelRoomReq req);

    /**
     * 保存房间预订
     * @param req 房间保存请求参数
     * @return 房间保存响应
     */
    CwSaveRoomRes saveRoom(CwSaveRoomReq req);

    /**
     * 保存团队预订
     * @param req 团队预订保存请求参数
     * @return 团队预订保存响应
     */
    CwColRsRes saveColReservation(CwColRsReq req);

    /**
     * 取消预订
     * @param req 预订取消请求参数
     * @return 预订取消响应
     */
    CwPmsCommonRes cancelReservation(CwCancelColRsReq req);

    /**
     * 应收账款支付
     * @param req 应收账款支付请求参数
     * @return 应收账款支付响应
     */
    CwPmsCommonRes arpay(CwOrderArPayReq req);

    /**
     * 房间消费记录
     * @param req 房间消费请求参数
     * @return 房间消费响应
     */
    CwOrderConsumptionRes roomConsumption(CwOrderConsumptionReq req);

    /**
     * 订单支付
     * @param req 订单支付请求参数
     * @return 订单支付响应
     */
    CwColPayRes orderPay(CwColPayReq req);

    /**
     * 取消收费
     * @param req 收费取消请求参数
     * @return 收费取消响应
     */
    CwPmsCommonRes cancelCharge(CwCancelChargeReq req);


    CwQueryColRes queryColRs(CwQueryColReq req);

    CwCheckInRes checkin(CwCheckInReq req);

    CwQueryCheckinOrderRes queryCheckinOrder(CwQueryCheckinOrderReq req);

    CwQueryHotelInfoRes queryHotelInfo(CwQueryHotelInfoReq req);

    // DSPMS APIs

    /**
     * DSPMS综合预订
     * 支持客房预订、餐饮预订、门票预订、导游预订等多种预订类型
     *
     * @param req DSPMS综合预订请求参数
     * @return DSPMS综合预订响应
     */
    CwCrsColRsSaveV1Res saveCwCrsColReservation(CwCrsColRsSaveV1Req req);

    /**
     * DSPMS客房预订
     * 专门用于客房预订的简化接口
     *
     * @param req DSPMS客房预订请求参数
     * @return DSPMS客房预订响应
     */
    CwCrsRoomSaveV1Res saveCwCrsRoom(CwCrsRoomRsSaveV1Req req);





}
