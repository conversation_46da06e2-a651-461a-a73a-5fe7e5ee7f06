package com.cw.service.config.ota;

import com.cw.exception.DefinedException;
import com.cw.pojo.dto.ota.req.*;
import com.cw.pojo.dto.ota.res.OtaQueryArAccountRes;
import com.cw.pojo.dto.ota.res.OtaQueryOrderRes;
import com.cw.pojo.dto.pms.res.building.BuildingListRes;
import com.cw.pojo.dto.pms.res.rate.RoomRateDetailRes;
import com.cw.pojo.dto.pms.res.reservation.RsOrderResult;
import com.cw.pojo.dto.pms.res.roomtype.RoomTypeListRes;
import com.cw.pojo.dto.ota.res.OtaChargeRes;

/**
 * OTA服务接口
 *
 * <AUTHOR>
 * @Create 2024/3/31
 */
public interface Pms_OtaService {
    /**
     * 创建订单
     */
    RsOrderResult createOrder(OtaCreateOrderReq req) throws DefinedException;

    /**
     * 取消订单
     */
    void cancelOrder(OtaCancelOrderReq req) throws DefinedException;

    /**
     * 查询订单
     */
    RsOrderResult queryOrder(OtaQueryOrderReq req);

    /**
     * 支付订单
     */
    void payOrder(OtaPayOrderReq req);

    /**
     * 退款订单
     */
    void refundOrder(OtaRefundOrderReq req);

    /**
     * 查询楼栋列表
     */
    BuildingListRes queryBuildings(OtaQueryBuildingReq req);

    /**
     * 查询楼栋下房型
     */
    RoomTypeListRes queryBuildingRoomTypes(OtaQueryRoomTypeReq req);

    /**
     * 查询指定日期范围房价
     */
    RoomRateDetailRes queryRoomRates(OtaQueryRateReq req);

    /**
     * 查询指定日期范围库存
     */
    RoomRateDetailRes queryInventory(OtaQueryInventoryReq req);

    OtaQueryOrderRes queryRoomOrders(OtaQueryRoomOrderReq req);

    OtaQueryArAccountRes queryReceivableAccounts(OtaQueryReceivableAccountReq req);

    OtaChargeRes arPay(OtaOrderArPayReq req);

    OtaChargeRes orderConsumption(OtaOrderConsumptionReq req);

    /**
     * 撤销消费
     */
    OtaChargeRes cancelCharge(OtaCancelChargeReq req);
} 