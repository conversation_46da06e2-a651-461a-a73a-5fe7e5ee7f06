package com.cw.service.config.reservationtype.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.config.exception.BizException;
import com.cw.core.SeqNoService;
import com.cw.entity.ReservationType;
import com.cw.mapper.ReservationMapper;
import com.cw.mapper.ReservationTypeMapper;
import com.cw.pojo.dto.pms.req.reservationtype.QueryReservationTypeReq;
import com.cw.pojo.dto.pms.req.reservationtype.ReservationTypeReq;
import com.cw.pojo.dto.pms.req.reservationtype.UpdateReservationTypeReq;
import com.cw.pojo.dto.pms.res.reservationtype.ReservationTypeListRes;
import com.cw.service.config.reservationtype.ReservationTypeService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.JpaUtil;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.jpa.DynamicSpecificationBuilder;
import com.cw.utils.jpa.Operator;
import com.cw.utils.jpa.SearchCriteria;
import com.cw.utils.pages.PageResUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 预定类型接口实现类
 * @Author: michael.pan
 * @Date: 2024/3/23 22:11
 */
@Service
public class ReservationTypeServiceImpl implements ReservationTypeService {

    @Resource
    private ReservationTypeMapper reservationTypeMapper;

    @Resource
    private ReservationMapper reservationMapper;


    @Override
    public void addReservationType(ReservationTypeReq reservationTypeReq) {
        String currentHotelId = GlobalContext.getCurrentHotelId();
        String description = reservationTypeReq.getDescription();
        int reservationTypeSize = reservationTypeMapper.countByHotelIdAndDescription(currentHotelId, description);
        //添加时校验该楼栋是否存在
        if(reservationTypeSize > 0){
            throw new BizException(StrUtil.format("预定类型为{}已存在，无法重复添加！", description));
        }

        ReservationType market = new ReservationType();
        BeanUtil.copyProperties(reservationTypeReq, market);
        market.setHotelId(currentHotelId);
        market.setCode(SeqNoService.getInstance().getUniqueCode(reservationTypeReq.getDescription(), market.getHotelId(), GlobalDataType.RSTYPE));
        market.setDescription(reservationTypeReq.getDescription());
        reservationTypeMapper.save(JpaUtil.appendEntity(market));

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.RSTYPE, currentHotelId);
    }

    @Override
    public void updateReservationType(UpdateReservationTypeReq reservationTypeReq) {
        // 先查询
        Optional<ReservationType> data = reservationTypeMapper.findById(reservationTypeReq.getId());
        reservationTypeMapper.save(JpaUtil.appendEntity(data, reservationTypeReq));

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.RSTYPE, GlobalContext.getCurrentHotelId());
    }

    @Override
    public ReservationTypeListRes listReservationType(QueryReservationTypeReq queryReservationTypeReq) {
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("description", queryReservationTypeReq.getDescription(), Operator.LIKE));
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", GlobalContext.getCurrentHotelId(), Operator.EQUAL));
        Specification<ReservationType> querySpecification = DynamicSpecificationBuilder.getQuerySpecification(ReservationType.class, conditionList);
        Page<ReservationType> reservationTypePage = reservationTypeMapper.findAll(querySpecification, JpaUtil.getPageRequest(queryReservationTypeReq.getPages()));
        return PageResUtil.fillPagesDate(reservationTypePage, ReservationTypeListRes.class);
    }

    @Override
    public void deleteReservationType(Long id) {
        String currentHotelId = GlobalContext.getCurrentHotelId();
        //删除是校验是否有关联数据
        Optional<ReservationType> reservationType = reservationTypeMapper.findById(id);
        if(!reservationType.isPresent()){
            throw new BizException(StrUtil.format("数据不存在，无法删除！"));
        }
        String code = reservationType.get().getCode();
        int reservationCount = reservationMapper.countByHotelIdAndReservationType(currentHotelId, code);
        if(reservationCount > 0){
            throw new BizException(StrUtil.format("该预定类型{}已使用，请先更改所有相关预定的预定类型，再尝试删除", code));
        }
        //删除
        reservationTypeMapper.deleteById(id);

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.RSTYPE, currentHotelId);
    }
}
