package com.cw.service.config.accountitem;

import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.pms.req.accountitem.AppAccountItemReq;
import com.cw.pojo.dto.pms.req.accountitem.UpdateAccountItemEntity;
import com.cw.pojo.dto.pms.res.accountitem.AccountItemListRes;
import com.cw.pojo.dto.pms.req.accountitem.AccountItemEntity;
import com.cw.pojo.dto.pms.req.accountitem.QueryAccountItemReq;

/**
 * @Description: 账项代码接口
 * @Author: michael.pan
 * @Date: 2024/3/21 23:40
 */
public interface AccountItemService {
    void addAccountItem(AccountItemEntity entity);

    void updateAccountItem(AccountItemEntity accountItemReq);

    AccountItemEntity loadAccountItem(Common_Load_Req req);

    AccountItemListRes listAccountItem(QueryAccountItemReq queryAccountItemReq);

    void deleteAccountItem(Long id);

    AccountItemListRes queryAccountItem(AppAccountItemReq appAccountItemReq);
}
