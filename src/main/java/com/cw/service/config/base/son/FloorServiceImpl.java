package com.cw.service.config.base.son;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.GlobalCache;
import com.cw.config.exception.BizException;
import com.cw.core.SeqNoService;
import com.cw.entity.Floor;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.PageReq;
import com.cw.pojo.dto.common.res.PageResponse;
import com.cw.pojo.dto.pms.req.others.FloorEntity;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.dto.pms.req.others.res.FloorListRes;
import com.cw.service.context.GlobalContext;
import com.cw.utils.enums.GlobalDataType;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/12/25 11:50
 **/
@Slf4j
@Service
public class FloorServiceImpl {

    @Autowired
    private DaoLocal<Floor> daoLocal;

    private boolean isNew(FloorEntity entity) {
        return entity.getId() == null || entity.getId() <= 0;
    }

    private FloorEntity toDto(Floor record) {
        if (record == null) {
            return null;
        }
        FloorEntity entity = new FloorEntity();
        BeanUtil.copyProperties(record, entity, CopyOptions.create().ignoreNullValue());
        return entity;
    }

    public FloorEntity save(FloorEntity entity) {
        Floor floor;
        if (isNew(entity)) {
            floor = new Floor();
            floor.setHotelId(GlobalContext.getCurrentHotelId());
            String code = SeqNoService.getInstance().getUniqueCode(entity.getDescription(), floor.getHotelId(), GlobalDataType.FLOOR);
            floor.setCode(code);
        } else {
            floor = daoLocal.find(Floor.class, entity.getId());
            if (floor == null) {
                throw new BizException("数据不存在");
            }
        }

        BeanUtil.copyProperties(entity, floor, CopyOptions.create().ignoreNullValue());

        daoLocal.merge(floor);

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.FLOOR, floor.getHotelId());

        return toDto(floor);
    }

    public void deleteById(Long id) {
        Floor record = daoLocal.find(Floor.class, id);
        if (record == null) {
            throw new BizException("数据不存在");
        }

        int count = daoLocal.getCountOption("select count(*) from Room where floor =?1 and hotelId=?2", record.getCode(), record.getHotelId());
        if (count > 0) {
            throw new BizException("该楼层已被使用，无法删除");
        }

        daoLocal.removeById(record);
        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.FLOOR, record.getHotelId());
    }

    public FloorEntity findById(Long id) {
        Floor record = daoLocal.find(Floor.class, id);
        if (record == null) {
            throw new BizException("数据不存在");
        }
        return toDto(record);
    }

    public FloorListRes pagelist(PageReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();
        Map<String, Object> queryMap = Maps.newHashMap();
        FloorListRes res = new FloorListRes();
        String jpql = "from Floor where hotelId=:hotelid ";
        CommonQueryPageReq pageReq = (CommonQueryPageReq) req;
        if (StrUtil.isNotBlank(pageReq.getSearchKey())) {
            jpql += " and description like :key ";
            queryMap.put("key", "%" + pageReq.getSearchKey() + "%");
        }
        jpql += " order by floor asc";
        queryMap.put("hotelid", hotelId);


        Page<Floor> page = daoLocal.getListPage(jpql, queryMap, req.getPages().getPageable());
        res.fillPageData(page);
        return res;
    }

    public void test() {
        FloorEntity newEntity = new FloorEntity();
        newEntity.setFloor(1);
        newEntity.setDescription("1层");

        FloorEntity saveEntity = save(newEntity);
        log.info("saveEntity:{}", saveEntity.getId());

        FloorEntity updateEntity = new FloorEntity();
        updateEntity.setId(saveEntity.getId());
        updateEntity.setFloor(2);
        updateEntity.setDescription("2层");
        save(updateEntity);

        PageResponse res = pagelist(new CommonQueryPageReq());

        log.info("res:{}", JSON.toJSONString(res));

        //FloorEntity findEntity=findById(updateEntity.getId());
        //log.info("findEntity:{}", JSON.toJSONString(findEntity));

        //deleteById(saveEntity.getId());

        int count = daoLocal.getCountOption("select count(*) from Floor where id=?1", saveEntity.getId());
        log.info("count:{}", count == 0 ? "删除成功" : "删除失败");


    }

}
