package com.cw.service.config.cashier;

import java.math.BigDecimal;
import java.util.Date;

public interface CashierRoomRateService {

	/**
	 * 检索出截止至date（含）未出房费的日期并入房费
	 *
	 * @param hotelId
	 * @param res_no
	 * @param date
	 */
	void postRoomRate(String hotelId, String res_no, Date date);

	/**
	 * 加收房费
	 * 
	 * @param hotelId
	 * @param res_no
	 * @param date
	 * @param addtionRadio
	 * @param withPkg
	 * @param remark
	 */
	void postAddtionRoomRate(String hotelId, String res_no, Date date, BigDecimal addtionRadio, boolean withPkg, String remark);
}
