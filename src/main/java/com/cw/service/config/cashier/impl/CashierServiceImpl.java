package com.cw.service.config.cashier.impl;

import static com.cw.utils.enums.pay.PayProcessStatus.PAY_SUCCESS;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.function.Function;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.xml.crypto.dsig.keyinfo.RetrievalMethod;

import com.cw.pojo.dto.pms.req.guest.BatchTransferAccountReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cw.cache.GlobalCache;
import com.cw.cache.impl.AccountItemCache;
import com.cw.cache.impl.RoomRateCache;
import com.cw.config.Cwconfig;
import com.cw.config.exception.BizException;
import com.cw.core.SeqNoService;
import com.cw.core.handler.pay.PayVendorSwitcher;
import com.cw.core.vendor.pay.PayVendorHandler;
import com.cw.entity.AccountItem;
import com.cw.entity.ArAccount;
import com.cw.entity.GuestAccounts;
import com.cw.entity.PkgDaily;
import com.cw.entity.Reservation;
import com.cw.entity.RoomsDaily;
import com.cw.mapper.AccountItemMapper;
import com.cw.mapper.AraccountMapper;
import com.cw.mapper.GuestAccountMapper;
import com.cw.mapper.NaDailyLogMapper;
import com.cw.mapper.Pkg_dailyMapper;
import com.cw.mapper.ReservationMapper;
import com.cw.mapper.Rooms_dailyMapper;
import com.cw.mapper.common.DaoLocal;
import com.cw.outsys.pay.StdPayParams;
import com.cw.outsys.pay.StdPayQueryParams;
import com.cw.outsys.pay.StdRefundParams;
import com.cw.pojo.dto.app.res.AppQrScanPayRes;
import com.cw.pojo.dto.app.res.AppQueryPayRes;
import com.cw.pojo.dto.pms.req.cashier.ArAgingAmountDetail;
import com.cw.pojo.dto.pms.req.cashier.ArAgingAmountsResp;
import com.cw.pojo.dto.pms.req.cashier.ArDiscountReq;
import com.cw.pojo.dto.pms.req.cashier.ArWriteOffReq;
import com.cw.pojo.dto.pms.req.cashier.CashierAccountReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostReq;
import com.cw.pojo.dto.pms.req.cashier.CashierPostResp;
import com.cw.pojo.dto.pms.req.guest.ArAccDepositTransferReq;
import com.cw.pojo.dto.pms.res.guest.ArAccTransferRes;
import com.cw.pojo.dto.pms.res.guest.MessageRsp;
import com.cw.pojo.dto.pms.res.statistic.StatARAmounts;
import com.cw.pojo.sqlresult.ArAmounts;
import com.cw.service.config.cashier.CashierService;
import com.cw.service.config.cashier.CashierSettingService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.IdUtil;
import com.cw.utils.JpaUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.enums.AccountItemEnum;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.VendorType;
import com.cw.utils.enums.pay.OnlinePayMethod;
import com.cw.utils.enums.pay.PayProcessStatus;
import com.cw.utils.tool.DefValData;
import com.cw.utils.tool.ObjTool;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 财务相关
 */
@Slf4j
@Service
public class CashierServiceImpl implements CashierService {

	@Resource
	private DaoLocal<?> dao;
	@Resource
	private GuestAccountMapper guestAccountMapper;
	@Resource
	private AraccountMapper araccountMapper;
	@Resource
	private AccountItemMapper accountItemMapper;
	@Resource
	private CashierSettingService cashierSettingService;
	@Resource
	private NaDailyLogMapper naDailyLogMapper;
	@Autowired
	ReservationMapper reservationMapper;
	@Autowired
	Rooms_dailyMapper roomsDailyMapper;
	@Autowired
	Pkg_dailyMapper pkgDailyMapper;
	@Autowired
	PayVendorSwitcher payVendorSwitcher;
	@Autowired
	SeqNoService seqNoService;
	@Autowired
	Cwconfig cwconfig;

	@Override

	public CashierPostResp post(@NotNull String hotelId, CashierPostReq req) {
		return post(hotelId, req, null);
	}

	/**
	 * 格式帐明细请求
	 *
	 * @param hotelId
	 * @param req
	 * @return
	 */
	private CashierAccountReq formatCashierAccountReq(String hotelId, CashierAccountReq req) {
		if (cashierSettingService.isCreditCode(hotelId, req.getDepartmentCode())) {// 付款，清空消费内容
			req.setPrice(BigDecimal.ZERO);
			req.setQuantity(BigDecimal.ONE);
		} else {// 消费 ，清空付款内容
			req.setCredit(BigDecimal.ZERO);
			req.setQueryPayMethod(0);
		}
		return req;
	}

	private boolean isPmsResNo(String resNo) {
		return StrUtil.isNotBlank(resNo)//
				&& !resNo.matches(".*[^a-zA-Z0-9].*")// 含有特殊字符的绕过预定存在检测
		;
	}

	@Override
	public CashierPostResp post(@NotNull String hotelId, CashierPostReq req, Consumer<GuestAccounts> accHandle) {
		if (StrUtil.isAllBlank(req.getReservationNumber(), req.getAr_no())) {
			throw new BizException("CSH_PST_KEY_NOT_FOUND", "预定号，应收号不能同时为空");
		}
		CashierPostResp rst = new CashierPostResp();
		rst.setAr_ids(new ArrayList<>());
		rst.setAcc_ids(new ArrayList<>());
		rst.setMaster_ids(new ArrayList<>());
		if (CollectionUtil.isEmpty(req.getAccounts())) {
			return rst;
		}
		if (isPmsResNo(req.getReservationNumber())) {
			Reservation res = reservationMapper.findByHotelIdAndReservationNumber(hotelId, req.getReservationNumber());
			if (null == res) {
				throw new BizException("CSH_PST_RES_NOT_FOUND", "无法定位预定");
			}
		}
		AccountItemCache cache_dept = GlobalCache.getDataStructure().getCache(GlobalDataType.ACCOUNTITEM);
		List<GuestAccounts> accounts = new ArrayList<>();
		DefValData<String, ArAmounts> arBals = new DefValData<String, ArAmounts>().setInitHandler(k -> new ArAmounts());
//		DefValData<String, LocalDate> nadate = new DefValData<String, LocalDate>().setInitHandler(hid -> {
//			return ObjTool.value(naDailyLogMapper.getLastNaDate(hid, TaskSequenceEnum.ACC_DAILY_TASK.getSeq().toString(), "成功"), o -> DateUtil.toLocalDateTime(o).toLocalDate(), () -> LocalDate.now());
//			return ObjTool.value(CalculateDate.asLocalDate(CalculateDate.reckonDay(CalculateDate.getSystemDate(), 5, -1)), e -> e, () -> LocalDate.now());
//		});
		Function<CashierAccountReq, GuestAccounts> req2acc = acc_req -> {
			GuestAccounts acc = new GuestAccounts();
			if (StrUtil.isEmpty(acc_req.getRemark())) {
				acc_req.setRemark(req.getRemark());
			}
			if (StrUtil.isEmpty(acc_req.getAr_no())) {
				acc_req.setAr_no(req.getAr_no());
			}
			formatCashierAccountReq(hotelId, acc_req);
			BeanUtil.copyProperties(acc_req, acc);
			if (StringUtils.isEmpty(acc.getDepartmentCode())) {
				throw new BizException("CSH_PST_CODE_NOT_FOUND", "付款码丢失，请检查传入参数");
			}
			AccountItem accItem = cache_dept.getRecord(hotelId, acc.getDepartmentCode());
			if (null == accItem) {
				throw new BizException("CSH_PST_ACC_ITEM_NOT_FOUND", StrUtil.format("{}-{} 账项码非法，请检查系统配置", hotelId, acc.getDepartmentCode()));
			}
			acc.setPaymethod(acc_req.getQueryPayMethod());
			acc.setAmount(getAmount(acc)); // 为了调试用的.后期会删除掉这个字段.
			if (!acc_req.isAmountCanBeZero() && getAmount(acc).compareTo(BigDecimal.ZERO) == 0) {
				throw new BizException("CSH_PST_AMT_ZRO", "非法金额，请检查传入参数");
			}
			if (!StrUtil.isEmpty(acc.getReservationNumber()) && !StrUtil.isEmpty(acc.getAr_no()) && acc.getCredit().compareTo(BigDecimal.ZERO) == 0) {
				throw new BizException("CSH_PST_DEBIT_AR_NO", "预定消费记录不可挂应收");
			}
			// 账目id
			acc.setAccountsId(IdUtil.genStrId());
			acc.setMaster_id(acc.getAccountsId());
			acc.setHotelId(hotelId);
			acc.setIncomeUser(GlobalContext.getCurrentUserId());
			acc.setReservationNumber(req.getReservationNumber());
			acc.setRes_no_org(acc.getReservationNumber());
			// 入账时间暂且为当前时间
			acc.setCreatedate(LocalDateTime.now());
//			if (null == acc.getBusiness_date()) {
//				acc.setBusiness_date(acc.getCreatedate().toLocalDate());
//			}
			if (null == req.getBusiness_date()) {
				acc.setBusiness_date(acc.getCreatedate().toLocalDate());
			} else {
				acc.setBusiness_date(DateUtil.toLocalDateTime(req.getBusiness_date()).toLocalDate());
			}
//			if (acc.getBusiness_date().compareTo(LocalDate.now()) < 0 && acc.getBusiness_date().compareTo(nadate.get(hotelId)) < 0) {
//				throw new BizException("CSH_PST_ACC_ITEM_NOT_FOUND", StrUtil.format("营业日期不能早于{}", nadate.get(hotelId)));
//			}
			if (StrUtil.isBlank(acc.getDescription())) {
				acc.setDescription(accItem.getDescription());
			}
			return acc;
		};
		for (CashierAccountReq acc_req : req.getAccounts()) {
			GuestAccounts acc = req2acc.apply(acc_req);
			accounts.add(acc);
			rst.getAcc_ids().add(acc.getAccountsId());
			rst.getMaster_ids().add(acc.getMaster_id());
			if (null != acc_req.getAdditionals()) {
				acc_req.getAdditionals().forEach(inc_req -> {
					GuestAccounts inc = req2acc.apply(inc_req);
					accounts.add(inc);
					inc.setMaster_id(acc.getMaster_id());
					rst.getAcc_ids().add(inc.getAccountsId());
					rst.getMaster_ids().add(inc.getMaster_id());
				});
			}
			if (!StrUtil.isEmpty(acc.getAr_no()) && !StrUtil.isEmpty(acc.getReservationNumber())) {// 生成一笔不隶属于预定的应收待核销记录
//				guestAccountMapper.getArAccBalance(hotelId, hotelId)r
				ArAccount account = araccountMapper.findByHotelIdAndCode(hotelId, acc.getAr_no());
				if (null != account.getCreditlimit() && BigDecimal.ZERO.compareTo(account.getCreditlimit()) <= 0) {// 启用限额
					BigDecimal over = account.getNoacc().subtract(getAmount(acc)).subtract(account.getCreditlimit());
					if (over.compareTo(BigDecimal.ZERO) > 0) {
						throw new BizException("AR_LMT", StrUtil.format("应收限额:{}, 已有未核销金额:{}, 超限:{}", account.getCreditlimit(), account.getNoacc(), over));
					}
				}
				GuestAccounts arRecord = new GuestAccounts();
				BeanUtil.copyProperties(acc, arRecord);
				acc.setAr_no("");// 挂靠预定帐脱离应收，由分离帐处理
				arRecord.setAccountsId(IdUtil.genStrId());
				arRecord.setMaster_id(arRecord.getAccountsId());
				arRecord.setReservationNumber("");
				arRecord.setSplit_from_id(acc.getAccountsId());
				arRecord.setPrice(getAmount(acc).negate());
				arRecord.setQuantity(BigDecimal.ONE);
				arRecord.setCredit(BigDecimal.ZERO);
				arRecord.setDepartmentCode(AccountItemEnum.ARDEBTS.getCode());
				arRecord.setAmount(getAmount(arRecord));
				accounts.add(arRecord);
				rst.getAr_ids().add(arRecord.getAccountsId());
			}
		}
		BigDecimal total = BigDecimal.ZERO;
		DefValData<String, List<BigDecimal>> deptAmts = new DefValData<String, List<BigDecimal>>().setInitHandler(k -> new ArrayList<>());
		for (GuestAccounts acc : accounts) {
			if (!StrUtil.isEmpty(acc.getAr_no())) {
				fillArAmounts(acc, arBals.get(acc.getAr_no()));
			}
			processPaymentMethod(acc);
			if (null != accHandle) {
				accHandle.accept(acc);
			}
			guestAccountMapper.save(JpaUtil.appendEntity(acc));
			BigDecimal amt = getAmount(acc);
			total = total.add(amt);
			deptAmts.get(acc.getDepartmentCode()).add(amt);
		}
		log.info("入账成功，{} 入账金额：{}，账项：{}", req.getReservationNumber(), total, accounts.stream().map(r -> r.getDepartmentCode()).toArray());
		// 同步预定余额
		if (isPmsResNo(req.getReservationNumber())) {
			synResBalance(hotelId, req.getReservationNumber());
		}
		arBals.getDataMap().forEach((ar_no, blcs) -> {
//			synArBalance(hotelId, ar_no, true);
			incArBalance(hotelId, ar_no, blcs);
		});
		// 同步长相代码统计（也可以不统计夜审再处理）
		deptAmts.getDataMap().forEach((code, amts) -> {
			accountItemMapper.addTodayAmount(hotelId, code, NumberUtil.add(amts.toArray(new BigDecimal[] {})));
		});
		return rst;
	}

	/**
	 * 处理对应付款方式，如微信线上，支付宝线上，信贷支付等非单纯GuestAccounts操作
	 *
	 * @param acc
	 */
	private void processPaymentMethod(GuestAccounts acc) {
		if (acc.getCredit().compareTo(BigDecimal.ZERO) == 0) {
			return;// 0余额不算付款，跳过步骤继续执行
		}
		try {
			PayVendorHandler payVendorHandler = null;
			if (PayProcessStatus.PAYMETHOD_ALI == acc.getPaymethod()) {// 支付宝
				payVendorHandler = payVendorSwitcher.getVendorHandler(VendorType.ALI_PAY);
			} else if (PayProcessStatus.PAYMETHOD_WX == acc.getPaymethod()) {
				payVendorHandler = payVendorSwitcher.getVendorHandler(VendorType.WX_PAY);
			}
			if (null == payVendorHandler) {
				return;// 没有对应付款方式，跳过步骤继续执行
			}
			if (acc.getCredit().compareTo(BigDecimal.ZERO) < 0) {// 付款
				if (guestAccountMapper.counts(acc.getHotelId(), acc.getReservationNumber(), acc.getDepartmentCode(), acc.getPaymethod(), acc.getTradeno()) > 0) {
					throw new BizException("QUERY_PAY_ERROR", StrUtil.format("({}) 已经存在，请刷新检查", acc.getTradeno()));
				}
				if (true) {
					return;// 线上支付交由前端定时出发，这里只作为数据记录
				}
				StdPayParams stdPayParams = new StdPayParams();
				String orderDesc = "酒店消费"; // ContentCacheTool.getPayOrderDesc(booking_rsList); TODO 后面更改为deptcode 的描述
				String prepeySeqno = null;
				if (acc.getPaymethod() == OnlinePayMethod.WX_SCANPAY || acc.getPaymethod() == OnlinePayMethod.ALI_SCANPAY) {
					prepeySeqno = seqNoService.getSequenceID_withOrderId(SystemUtil.SequenceKey.PREPAY, acc.getTradeno());
				} else {
					prepeySeqno = seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);
				}
				seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY);
				Long payexpireTime = 1000 * 60 * 2L; // 扫码器支付.一分钟过期.普通订单支付5分钟过期
				payexpireTime = new Date().getTime() + payexpireTime;
				stdPayParams.setProjectId(SystemUtil.CONSOLEHOTELID); // 目前按服务商模式做支付配置
				stdPayParams.setOrderids(Arrays.asList(acc.getReservationNumber()));//
				stdPayParams.setOutTradeNo(prepeySeqno);
				stdPayParams.setOrderDesc(orderDesc);
				stdPayParams.setTotalPay(acc.getCredit().negate());
				stdPayParams.setPayerId("");// openid
				stdPayParams.setExpireTime(payexpireTime);// getPayExpireTimeStamp(booking_rsList, projectId)
				stdPayParams.setNotifyDomain(cwconfig.getDomain());
				stdPayParams.setPaymode(acc.getPaymethod());
				stdPayParams.setPayscene(0);
				stdPayParams.setQrAuthCode(acc.getTradeno());
				stdPayParams.setDeptCode(acc.getDepartmentCode());
				stdPayParams.setHotelId(acc.getHotelId());
				AppQrScanPayRes scanPayRes = payVendorHandler.scanPay(stdPayParams);
				if (null == scanPayRes) {
					throw new BizException("QUERY_PAY_ERROR", "付款方式未实现，请尝试其他支付方式。");
				}
				AppQueryPayRes queryPay = null;
				if (scanPayRes.getScan_status_code() == PayProcessStatus.PAY_WAIT) {
					for (long l : Arrays.asList(5, 5, 5, 10, 10, 20, 20)) {
						StdPayQueryParams params = new StdPayQueryParams();
						params.setProjectId(SystemUtil.CONSOLEHOTELID); // 暂时配置为系统级别的支付
						params.setOutTradeNo(prepeySeqno);
						params.setLscanPay(true);
						queryPay = payVendorHandler.queryPay(params);
						if (queryPay.getScan_status_code() == PAY_SUCCESS || queryPay.getScan_status_code() == PayProcessStatus.PAY_FAIL) {
							break;
						}
						Thread.sleep(l * 1000l);
					}
				} else if (scanPayRes.getScan_status_code() == PayProcessStatus.PAY_SUCCESS) {
					queryPay = new AppQueryPayRes();
					BeanUtil.copyProperties(scanPayRes, queryPay);
				} else {
					throw new BizException("QUERY_PAY_ERROR", scanPayRes.getAlert_reason());
				}
				if (queryPay.getPayStatus() == PAY_SUCCESS) {
					acc.setTradeno(queryPay.getOutTradeNo());
					acc.setSerialno(queryPay.getTransId());
				} else {
					throw new BizException("QUERY_PAY_ERROR", queryPay.getAlert_reason());
				}
			} else if (acc.getCredit().compareTo(BigDecimal.ZERO) > 0) {// 退款
				StdPayQueryParams queryParams = new StdPayQueryParams();
				queryParams.setProjectId(SystemUtil.CONSOLEHOTELID); // 暂时配置为系统级别的支付
				queryParams.setOutTradeNo(acc.getTradeno());
				queryParams.setLscanPay(true);
				AppQueryPayRes queryPay = payVendorHandler.queryPay(queryParams);
				StdRefundParams params = new StdRefundParams();
				params.setAttachProjectId(SystemUtil.CONSOLEHOTELID);
				params.setOrgAmount(NumberUtil.toBigDecimal(queryPay.getTotalAmount()));
				params.setRefund(acc.getCredit());
				params.setTransactionId(queryPay.getTransId());
				params.setOutTradeNo(acc.getTradeno());
				params.setOutRefundNo(seqNoService.getSequenceID(SystemUtil.SequenceKey.PREPAY));
				payVendorHandler.refundPay(params);
//				throw new BizException("QUERY_RNF_ERROR", "退款功能未实现，下个版本一定实现。");
			}
		} catch (Exception e) {
			throw new BizException("QUERY_PAY_ERROR", e.getMessage(), e);
		}
	}

	/**
	 * 同步预定余额
	 *
	 * @param res_no
	 * @return
	 */
	private BigDecimal synResBalance(@NotNull String hotelId, String res_no) {
		Object[] amounts = dao.getObject("select sum(price*quantity),sum(credit) from GuestAccounts where hotelId=?1 and reservationNumber=?2", hotelId, res_no);
		if (null != amounts) {
			BigDecimal consume = NumberUtil.toBigDecimal((BigDecimal) amounts[0]);
			BigDecimal credit = NumberUtil.toBigDecimal((BigDecimal) amounts[1]); // (BigDecimal) amounts[1];
			BigDecimal balance = consume.add(credit);
			Map<String, Object> params = new HashMap<>();
			params.put("blc", balance);
			params.put("consume", consume);
			params.put("credit", credit);
			params.put("hotelId", hotelId);
			params.put("res_no", res_no);
			dao.executeUpdate("update Reservation set balance=:blc,consume=:consume,payment=:credit where hotelId=:hotelId and reservationNumber=:res_no", params);// 前端用了三个统计字段.这里就一起改咯
			return balance;
		}
		return BigDecimal.ZERO;
	}

	/**
	 * 全方式同步应收余额
	 *
	 * @param hotelId
	 * @param arno
	 * @param blc
	 * @param currOnly 仅同步noacc（待核销）、balance（可支付余额）
	 */
	public void synArBalance(String hotelId, String arno, boolean currOnly) {
		ArAmounts blc = new ArAmounts();
		Object[] sumaries;
		long l;
//		String hql = "select COALESCE( sum(credit) , 0)"// 0,累计充值金额
//				+ ", COALESCE( sum(case when res_no_org<>'' then (price*quantity+credit) else 0 end) , 0)"// 1,累计（来自预定）挂账金额
//				+ ", COALESCE( sum(case when res_no_org<>'' and price*quantity+credit+payed_amt+adjust_amt=0 then (price*quantity+credit) else 0 end) , 0)"// 2,累计（来自预定）已核销金额
////				+ ", COALESCE( sum(case when (ar_payfor_id is null or ar_payfor_id='') and price*quantity+credit<0 then (price*quantity+credit) else 0 end) , 0)"// 3,可用于支付企业余额
//				+ ", COALESCE( sum(case when (ar_payfor_id is null or ar_payfor_id='') and credit<0 then (credit) else 0 end) , 0)"// 3,可用于支付企业余额
////				+ ", COALESCE( sum(adjust_amt) , 0)"// 4,累计调整金额
//				+ ", COALESCE( sum(case when res_no_org<>'' then 0 else price*quantity end) , 0)"// 4,累计调整金额
//				+ " from guest_accounts ga left join (SELECT ar_payfor_id AS payfor_id"//
////				+ ", SUM((case when res_no_org<>'' then (price*quantity) else 0 end)+credit) AS payed_amt"// 9550上线后deptcodes_ardebit条件可以废弃department_code in (:deptcodes_ardebit)
//				+ ", SUM(credit) AS payed_amt"//
////				+ ", SUM(case when (res_no_org is null or res_no_org='') then (price*quantity) else 0 end) AS adjust_amt"// department_code not in (:deptcodes_ardebit)
//				+ ", SUM(price*quantity) AS adjust_amt"//
//				+ ", COUNT(1) AS payed_cnt"//
//				+ " FROM guest_accounts WHERE hotelId=:hotelId and ar_no=:ar_no AND ar_payfor_id<>'' GROUP BY ar_payfor_id) AS payeds"//
//				+ " ON payeds.payfor_id=ga.master_id"//
//				+ " where hotelId=:hotelId and ar_no=:ar_no";
//		l = System.currentTimeMillis();
//		sumaries = dao.getNativeObject(hql, Map.of(//
//				"hotelId", hotelId//
//				, "ar_no", arno//
////				, "deptcodes_ardebit", CollectionUtil.toList(AccountItemEnum.ARDEBTS.getCode())//
//		));
//		blc.setCredit(ObjectUtil.defaultIfNull(sumaries, r -> (BigDecimal) r[0], BigDecimal.ZERO));
//		blc.setWriteoff(ObjectUtil.defaultIfNull(sumaries, r -> ((BigDecimal) r[2]), BigDecimal.ZERO));
//		blc.setNoacc(ObjectUtil.defaultIfNull(sumaries, r -> ((BigDecimal) r[1]).subtract((BigDecimal) r[2]), BigDecimal.ZERO));
//		blc.setBalance(ObjectUtil.defaultIfNull(sumaries, r -> (BigDecimal) r[3], BigDecimal.ZERO));
//		blc.setAdjust(ObjectUtil.defaultIfNull(sumaries, r -> (BigDecimal) r[4], BigDecimal.ZERO));
//		log.info("{} {} {} {}", arno, sumaries, blc, System.currentTimeMillis() - l);
		// 累计核销:
		// BigDecimal payed_amt = blc.getWriteoff().add(blc.getNoacc());
		l = System.currentTimeMillis();
		sumaries = dao.getObject("select COALESCE( sum(credit) , 0)"// 0,累计充值金额
				+ ", COALESCE( sum(case when res_no_org<>'' then (price*quantity+credit) else 0 end) , 0)"// 1,累计（来自预定）挂账金额
				+ ", COALESCE( sum(case when res_no_org<>'' then amount_write_off else 0 end) , 0)"// 2,累计（来自预定）已核销金额
//				+ ", COALESCE( sum(case when (ar_payfor_id<>'') then (price*quantity+credit) else 0 end) , 0)"// 调整2
				+ ", COALESCE( sum(case when (ar_payfor_id<>'' or credit>0) then 0 else credit end) , 0)"// 3,可用于支付企业余额
				+ ", COALESCE( sum(case when res_no_org<>'' then 0 else (price*quantity) end) , 0)"// 4,累计调整金额
				+ " from GuestAccounts where hotelId=:hotelId and ar_no=:ar_no", Map.of("hotelId", hotelId, "ar_no", arno));
		blc = new ArAmounts();
		blc.setCredit(ObjectUtil.defaultIfNull(sumaries, r -> NumberUtil.toBigDecimal((Number) r[0]), BigDecimal.ZERO));
		blc.setWriteoff(ObjectUtil.defaultIfNull(sumaries, r -> NumberUtil.toBigDecimal((Number) r[2]), BigDecimal.ZERO));
		blc.setNoacc(ObjectUtil.defaultIfNull(sumaries, r -> NumberUtil.toBigDecimal((Number) r[1]).add(NumberUtil.toBigDecimal((Number) r[2])), BigDecimal.ZERO));
		blc.setBalance(ObjectUtil.defaultIfNull(sumaries, r -> NumberUtil.toBigDecimal((Number) r[3]), BigDecimal.ZERO));
		blc.setAdjust(ObjectUtil.defaultIfNull(sumaries, r -> NumberUtil.toBigDecimal((Number) r[4]), BigDecimal.ZERO));
		log.info("{} {} {} {}", arno, sumaries, blc, System.currentTimeMillis() - l);
		ArAccount cust = araccountMapper.findByHotelIdAndCode(hotelId, arno);
		if (currOnly) {
//			araccountMapper.synBalance(hotelId, arno, blc.getNoacc(), blc.getBalance());
			cust.setBalance(blc.getBalance());
			cust.setNoacc(blc.getNoacc());
		} else {
//			araccountMapper.synBalance(hotelId, arno, blc.getNoacc(), blc.getCredit(), blc.getAdjust(), blc.getWriteoff(), blc.getBalance());
			cust.setNoacc(blc.getNoacc());
			cust.setCredit(blc.getCredit());
			cust.setAdjust(blc.getAdjust());
			cust.setWriteoff(blc.getWriteoff());
			cust.setBalance(blc.getBalance());
			try {
				l = System.currentTimeMillis();
				StatARAmounts statARAmounts = JSONUtil
						.toBean((String) dao.getObject("select amounts_total from DailyStat_AR where hotelId=:hotelId and ar_no=:ar_no order by business_date desc",
								Map.of("hotelId", hotelId, "ar_no", arno)), StatARAmounts.class);
				if (null != statARAmounts.getHistoryBalance()) {
					cust.setCredit(NumberUtil.add(blc.getCredit(), statARAmounts.getHistoryBalance().getCredit()));
					cust.setAdjust(NumberUtil.add(blc.getAdjust(), statARAmounts.getHistoryBalance().getAdjust()));
					cust.setWriteoff(NumberUtil.add(blc.getWriteoff(), statARAmounts.getHistoryBalance().getWriteoff()));
				}
				log.info("{} {} {}", arno, statARAmounts.getHistoryBalance(), System.currentTimeMillis() - l);
			} catch (Throwable e) {
			}
		}
		araccountMapper.saveAndFlush(cust);
	}

	private ArAmounts arAmount(GuestAccounts acc) {
		ArAmounts rst = new ArAmounts();
		rst.setCredit(acc.getCredit());
		if (StrUtil.isNotBlank(acc.getRes_no_org())) {
			rst.setWriteoff(acc.getAmount_write_off());
			rst.setNoacc(getAmount(acc).add(acc.getAmount_write_off()));
		} else {
			rst.setAdjust(acc.getPrice().multiply(acc.getQuantity()));
		}
		if (StrUtil.isNotBlank(acc.getAr_payfor_id()) && NumberUtil.isLess(acc.getCredit(), BigDecimal.ZERO)) {
			rst.setBalance(acc.getCredit());
		}
		return rst;
	}

	private void fillArAmounts(GuestAccounts acc, ArAmounts blc) {
		BigDecimal amt = getAmount(acc);
		if (0 == acc.getCredit().compareTo(BigDecimal.ZERO)) {
			if (StrUtil.isBlank(acc.getRes_no_org())) {
				blc.setAdjust(blc.getAdjust().add(amt));
			}
		} else {
			blc.setBalance(blc.getBalance().add(amt));
			blc.setCredit(blc.getCredit().add(amt));
		}
		if (amt.compareTo(BigDecimal.ZERO) > 0) {
			blc.setNoacc(blc.getNoacc().add(amt));
		}
//		if (StrUtil.isNotBlank(acc.getReservationNumber())) {// 来自预定优先，正负都是挂账
//			blc.noacc = blc.noacc.add(amt);
//		} else {
//			if (0 == acc.getCredit().compareTo(BigDecimal.ZERO)) {
//				blc.adjust = blc.adjust.add(amt);
//			} else {
//				blc.credit = blc.credit.add(amt);
//			}
//		}
	}

	/**
	 * 增量方式同步应收余额
	 *
	 * @param hotelId
	 * @param arno
	 * @param blc
	 */
	private void incArBalance(String hotelId, String arno, ArAmounts blc) {
////		araccountMapper.incArBalance(hotelId, arno, blc.getNoacc(), blc.getCredit(), blc.getAdjust(), blc.getWriteoff(), blc.getBalance());
//		ArAccount cust = araccountMapper.findByHotelIdAndCode(hotelId, arno);
//		cust.setNoacc(cust.getNoacc().add(blc.getNoacc()));
//		cust.setCredit(cust.getCredit().add(blc.getCredit()));
//		cust.setAdjust(cust.getAdjust().add(blc.getAdjust()));
//		cust.setWriteoff(cust.getWriteoff().add(blc.getWriteoff()));
//		cust.setBalance(cust.getBalance().add(blc.getBalance()));
//		araccountMapper.saveAndFlush(cust);
		synArBalance(hotelId, arno, false);
	}

	@Override
	public CashierPostResp refund(@NotNull String hotelId, @NotNull String acc_id, BigDecimal refund_amount, String remark) {
		return refund(hotelId, acc_id, null == refund_amount ? null
				: ListUtil.toList(new CashierAccountReq()//
						.setQuantity(BigDecimal.ONE)//
						.setPrice(refund_amount)//
						.setPrice(refund_amount)//
						.setRemark(remark)//
				));
	}

	public CashierPostResp refund(@NotNull String hotelId, @NotNull String acc_id, List<CashierAccountReq> accounts) {
		GuestAccounts acc = guestAccountMapper.getGuestAccounts(hotelId, acc_id);
		if (null == acc) {
			throw new BizException("ACC_NOT_FND", "目标账目缺失");
		}
		if (null == accounts) {
			accounts = ListUtil.toList(new CashierAccountReq()//
					.setRemark("退款")//
					.setDepartmentCode(acc.getDepartmentCode())//
					.setQuantity(acc.getQuantity())//
					.setPrice(acc.getPrice().negate())//
					.setCredit(acc.getCredit().negate())//
			);
		}
		accounts.stream().filter(a -> StrUtil.isBlank(a.getDepartmentCode())).forEach(a -> a.setDepartmentCode(acc.getDepartmentCode()));
		BigDecimal refund_amount = BigDecimal.ZERO;
		for (CashierAccountReq accreq : accounts) {
			formatCashierAccountReq(hotelId, accreq);
			refund_amount = accreq.getPrice().multiply(accreq.getQuantity()).add(accreq.getCredit()).add(refund_amount);
		}
		BigDecimal accAmt = getAmount(acc);
		Boolean is_cancel_mod = 0 == accAmt.add(refund_amount).compareTo(BigDecimal.ZERO);// 整单取消模式
		if (accAmt.multiply(refund_amount).compareTo(BigDecimal.ZERO) > 0) {
			throw new BizException("AMOUNT_ILLEGAL", "请正确填写退款金额");
		}
		if (refund_amount.abs().compareTo(accAmt.abs()) > 0) {
			throw new BizException("AMOUNT_ILLEGAL", "不允许超额退款");
		}
		List<GuestAccounts> arAccs = StrUtil.isNotBlank(acc.getAr_no())// 本帐就是应收帐
				? Arrays.asList(acc)// 直接定位本帐
				: guestAccountMapper.getArDebitDetails(hotelId, acc_id, AccountItemEnum.ARDEBTS.getCode());// 否则取对应应收待冲销记录
		GuestAccounts ar_acc = null;
		if (arAccs.size() > 1) {
			throw new BizException("MUL_ARACC", "存在多个应收明细");
		} else if (1 == arAccs.size()) {
			ar_acc = arAccs.iterator().next();
		}
		// 如果是挂账，检测余额
		if (null != ar_acc) {
			if (!is_cancel_mod) {
				throw new BizException("AR_ACC_NOT_CXL", "应收帐不支持部分退款");
			}
			BigDecimal arBalance = guestAccountMapper.getArAccBalance(hotelId, ar_acc.getMaster_id());
			if (null == arBalance) {
				throw new BizException("AR_ACC_NOT_FND", "应收帐缺失");
			}
			if (ar_acc.getAmount_write_off().compareTo(BigDecimal.ZERO) != 0) {
				throw new BizException("AR_ACC_PAYED", "应收帐已核销完成");
			}
			if (arBalance.compareTo(BigDecimal.ZERO) == 0) {
				throw new BizException("AR_ACC_PAYED", "操作失败： 应收帐已核销完成");
			}
		}
		CashierPostReq cashierPostReq = new CashierPostReq();
		cashierPostReq.setReservationNumber(acc.getReservationNumber());
		cashierPostReq.setAr_no(ObjectUtil.defaultIfNull(ar_acc, ar -> ar.getAr_no(), ""));
//		cashierPostReq.setRemark(remark);
		cashierPostReq.setAccounts(new ArrayList<>());
		cashierPostReq.getAccounts().addAll(accounts);
		for (CashierAccountReq accreq : accounts) {
			accreq.setAr_no(acc.getAr_no());
			accreq.setQueryPayMethod(acc.getPaymethod());
			accreq.setTradeno(acc.getTradeno());
		}
//		CashierAccountReq accountReq = new CashierAccountReq();
//		cashierPostReq.getAccounts().add(accountReq);
//		accountReq.setAr_no(acc.getAr_no());
//		accountReq.setPrice(ObjectUtil.defaultIfNull(refund_amount, acc.getPrice()));
//		accountReq.setQuantity(acc.getQuantity().negate());
//		accountReq.setCredit(ObjectUtil.defaultIfNull(refund_amount, acc.getCredit().negate()));
//		accountReq.setQueryPayMethod(acc.getPaymethod());
//		accountReq.setTradeno(acc.getTradeno());
//		accountReq.setDepartmentCode(acc.getDepartmentCode());
		CashierPostResp rst = post(hotelId, cashierPostReq, a -> {
			a.setSplit_from_id(acc_id);
			a.setRes_no_org(a.getReservationNumber());
			if (is_cancel_mod) {
				a.setInternal(true);
			}
		});
		// 整笔取消模式下
		if (is_cancel_mod) {
			if (null != ar_acc) {// 挂账自动冲销, 金额为正的作为被冲销
				if (getAmount(ar_acc).compareTo(BigDecimal.ZERO) > 0) {
					ar_write_off(hotelId, ar_acc, rst.getAr_ids());
					ArAmounts synBalance = new ArAmounts();
					synBalance.setWriteoff(getAmount(ar_acc));
					synBalance.setNoacc(synBalance.getWriteoff().negate());
					synBalance.setAdjust(synBalance.getNoacc());
					incArBalance(hotelId, ar_acc.getAr_no(), synBalance);
				} else {
					Collection<String> payment_ids = new ArrayList<>();
					payment_ids.add(acc_id);
					for (String id : rst.getAcc_ids()) {
						ar_write_off(hotelId, guestAccountMapper.getGuestAccounts(hotelId, id), payment_ids);
					}
				}
			}
			// 完事后隐藏账目
			List<GuestAccounts> accList = guestAccountMapper.getRelationAccounts(hotelId, null == ar_acc ? Arrays.asList(acc_id) : Arrays.asList(acc_id, ar_acc.getMaster_id()));
			accList.forEach(e -> {
				JpaUtil.appendEntity(e);
				e.setInternal(true);
			});
			guestAccountMapper.saveAll(accList);
//			dao.executeUpdate("update GuestAccounts set internal=:_true where hotelId=:hotelId and master_id in (:acc_ids)"//
//					, Map.of("hotelId", hotelId//
//							, "acc_ids", null == ar_acc ? Arrays.asList(acc_id) : Arrays.asList(acc_id, ar_acc.getMaster_id())//
//							, "_true", true//
//					));
		}
		return rst;
	}

	@Override
	public int transfer_res(@NotNull String hotelId, @NotNull Collection<String> acc_ids, String toResNo, String remark) {
		if (CollectionUtil.isEmpty(acc_ids)) {
			throw new BizException("TRANSFER_NOTHING", "无转账记录");
		}
		{
			Map<String, Object> params = new HashMap<>();
			params.put("res_no", toResNo);
			params.put("hotelId", hotelId);
			params.put("status", Arrays.asList(0, 1));
			if (StringUtils.isEmpty(toResNo) || null == dao
					.getObject("select reservationNumber from Reservation where hotelId=:hotelId and reservationNumber=:res_no and reservationStatus in (:status)", params)) {
				throw new BizException("TRANSFER_RES_NO_EMPTY", "找不到合适预定");
			}
		}
		Collection<String> resNosSynNeed = new HashSet<>(guestAccountMapper.getResNosByAccIds(hotelId, acc_ids));
		resNosSynNeed.remove("");
		resNosSynNeed.add(toResNo);
//		Map<String, Object> params = new HashMap<>();
//		params.put("res_no", toResNo);
//		params.put("add_remark", remark);
//		params.put("hotelId", hotelId);
//		params.put("ids", acc_ids);
//		params.put("history", false);
//		int count = dao.executeUpdate(
//				"UPDATE GuestAccounts set reservationNumber=:res_no, remark=SUBSTRING(CONCAT(remark, ' ', :add_remark) , 1, 255) where hotelId=:hotelId and master_id in (:ids)"
//						+ " and reservationNumber<>'' and (history is null or history=:history)"// 排除单纯应收记录
//				, params);
		List<GuestAccounts> accList = guestAccountMapper.getRelationAccounts(hotelId, acc_ids);
		accList.stream().filter(e -> StrUtil.isNotBlank(e.getReservationNumber()) && !e.isHistory()).forEach(e -> {
			JpaUtil.appendEntity(e);
			e.setReservationNumber(toResNo);
			e.setRemark(StrUtil.concat(true, e.getRemark(), " ", remark).trim());
		});
		int count = guestAccountMapper.saveAll(accList).size();
		// 同步预定余额
		resNosSynNeed.forEach(res_no -> synResBalance(hotelId, res_no));
		return count;
	}

	@Override
	public int batch_transfer(String hotelId, BatchTransferAccountReq req) {
		// 前台团队批量结账.将选中的预订中.全部的账目转移到付钱最多的一个订单中(一般是导游预订或者直连下来付款的预订)

        String jpql = " from Reservation where hotelId=:hotelId and reservationNumber in (:res_nos) and reservationStatus in (0,1) order by payment asc";
		List<Reservation> rslist = dao.getList(jpql, Map.of("hotelId", hotelId, "res_nos", req.getRegNos()));
		if (rslist.size() != req.getRegNos().size() || rslist.size() < 2) {
			throw new BizException("TRANSFER_RES_NO_EMPTY", "不满足批量转账条件");
		}
		String targetResNo = rslist.get(0).getReservationNumber();
		List<String> otherResNos = rslist.subList(1, rslist.size()).stream().map(Reservation::getReservationNumber).collect(Collectors.toList());
		// TODO 后期优化.将订单跟账目表关联.获取选中预订的所有账目ID.

		List<String> acc_ids = guestAccountMapper.getRsGuestAccounts(hotelId, otherResNos);
		int count = transfer_res(hotelId, acc_ids, targetResNo, req.getRemark());

		log.info("批量转账成功,转账目标预定号:{},原始订单号:{},转账账目:{}", targetResNo, otherResNos, acc_ids.size());

		return count;
	}

	private BigDecimal getAmount(GuestAccounts acc) {
		return acc.getPrice().multiply(acc.getQuantity()).add(acc.getCredit());
	}

	/**
	 * 拆分帐
	 *
	 * @param hotelId
	 * @param acc_id       需拆分的帐
	 * @param split_amount 需拆分出来的金额
	 * @return 新帐ID
	 */
	private String split(@NotNull String hotelId, @NotNull String acc_id, BigDecimal split_amount, String remark) {
		Map<String, GuestAccounts> orgAccs = new HashMap<>();
		guestAccountMapper.getRelationAccounts(hotelId, CollectionUtil.toList(acc_id)).forEach(acc -> {
			orgAccs.put(acc.getAccountsId(), acc);
		});
		if (orgAccs.isEmpty()) {
			throw new BizException("GUEST_ACCOUNTS_NOT_FOUND", "客人账目不存在");
		}
		BigDecimal total = BigDecimal.ZERO;
		for (GuestAccounts acc : orgAccs.values()) {
			total = getAmount(acc).add(total);
		}
		BigDecimal radio = split_amount.divide(total, 6, RoundingMode.HALF_UP);
//		Map<String, BigDecimal> splitAmounts = new HashMap<>();
//		orgAccs.forEach((id, acc) -> {
//			splitAmounts.put(id, getAmount(acc).multiply(radio).setScale(2, RoundingMode.HALF_UP));
//		});
		List<GuestAccounts> new_accs = // new ArrayList<>();
				copyCreateAccounts(orgAccs.values(), (orgEntity, newEntity) -> {
					newEntity.setRemark(remark);
//			entity.setPrice(splitAmounts.getOrDefault(org.getAccountsId(), BigDecimal.ZERO).divide(entity.getQuantity(), 2, RoundingMode.HALF_UP));
					newEntity.setPrice(newEntity.getPrice().multiply(radio).setScale(2, RoundingMode.HALF_UP));
					newEntity.setCredit(newEntity.getCredit().multiply(radio).setScale(2, RoundingMode.HALF_UP));
					orgEntity.setPrice(orgEntity.getPrice().subtract(newEntity.getPrice()));
					orgEntity.setCredit(orgEntity.getCredit().subtract(newEntity.getCredit()));
					return true;
				});
//		Map<String, String> ids_hedge = new HashMap<>();
//		orgAccs.values().forEach(org -> {
//			ids_hedge.put(org.getAccountsId(), IdUtil.genStrId());
//			GuestAccounts entity = new GuestAccounts();
//			BeanUtil.copyProperties(org, entity);
//			// 记录分离原始来源
//			entity.setId(null);
////			entity.setAr_payfor_id(null);
//
//			entity.setSplit_from_id(StrUtil.isBlank(org.getSplit_from_id()) ? acc_id : org.getSplit_from_id());
//			entity.setRemark(remark);
//			new_accs.add(entity);
////			entity.setPrice(splitAmounts.getOrDefault(org.getAccountsId(), BigDecimal.ZERO).divide(entity.getQuantity(), 2, RoundingMode.HALF_UP));
//			entity.setPrice(entity.getPrice().multiply(radio).setScale(2, RoundingMode.HALF_UP));
//			entity.setCredit(entity.getCredit().multiply(radio).setScale(2, RoundingMode.HALF_UP));
//			org.setPrice(org.getPrice().subtract(entity.getPrice()));
//			org.setCredit(org.getCredit().subtract(entity.getCredit()));
//		});
//		// 替换新记录的acc-id
//		new_accs.forEach(e -> {
//			e.setAccountsId(ids_hedge.getOrDefault(e.getAccountsId(), ""));
//			e.setParent_id(ids_hedge.getOrDefault(e.getParent_id(), ""));
//			e.setMaster_id(ids_hedge.getOrDefault(e.getMaster_id(), ""));
//		});
		List<GuestAccounts> updates = new ArrayList<>();
		Arrays.asList(orgAccs.values(), new_accs).forEach(lst -> {
			lst.forEach(e -> {
				updates.add(JpaUtil.appendEntity(e));
//				guestAccountMapper.save(JpaUtil.appendEntity(e));
			});
		});
		guestAccountMapper.saveAll(updates);
		return new_accs.iterator().next().getMaster_id();// ids_hedge.get(acc_id);
	}

	interface CopyHandle {
		/**
		 * @param orgEntity
		 * @param newEntity
		 * @return 是否加加入新增队列
		 */
		boolean copy(GuestAccounts orgEntity, GuestAccounts newEntity);
	}

	private List<GuestAccounts> copyCreateAccounts(Collection<GuestAccounts> orgAccs, CopyHandle handle) {
		List<GuestAccounts> new_accs = new ArrayList<>();// 替换新记录的acc-id
		DefValData<String, String> ids_hedge = new DefValData<String, String>().setInitHandler(k -> StrUtil.isBlank(k) ? "" : IdUtil.genStrId());
		orgAccs.forEach(org -> {
			GuestAccounts entity = new GuestAccounts();
			BeanUtil.copyProperties(org, entity);
			// 记录分离原始来源
			entity.setId(null);
			entity.setSplit_from_id(StrUtil.isBlank(org.getSplit_from_id()) ? org.getMaster_id() : org.getSplit_from_id());
			entity.setAccountsId(ids_hedge.get(entity.getAccountsId()));
//			entity.setParent_id(ids_hedge.get(entity.getParent_id()));
			entity.setMaster_id(ids_hedge.get(entity.getMaster_id()));
			if (handle.copy(org, entity)) {
//				ids_hedge.put(entity.getAccountsId(), IdUtil.genStrId());// 发神经的一段，结果就是每次accid都分配一个新号，搞懂当时怎么考虑的
				new_accs.add(entity);
			}
		});

		new_accs.forEach(e -> {
		});
		return new_accs;
	}

	/**
	 * 取消
	 */
	public CashierPostResp cancel(@NotNull String hotelId, @NotNull String acc_id, List<CashierAccountReq> accounts) {
		GuestAccounts acc = guestAccountMapper.getGuestAccounts(hotelId, acc_id);
		if (null == acc) {
			throw new BizException("ACC_NOT_FND", "目标账目缺失");
		}
		if (null != accounts) {
			BigDecimal refund_amount = getAmount(acc);
			for (CashierAccountReq accreq : accounts) {
				formatCashierAccountReq(hotelId, accreq);
				refund_amount = accreq.getPrice().multiply(accreq.getQuantity()).add(accreq.getCredit()).add(refund_amount);
			}
			if (0 != refund_amount.compareTo(BigDecimal.ZERO)) {
				accounts.add(new CashierAccountReq()//
						.setRemark("退款")//
						.setDepartmentCode(acc.getDepartmentCode())//
						.setQuantity(acc.getQuantity())//
						.setPrice(refund_amount.negate())//
						.setCredit(refund_amount.negate())//
				);
			}
		}
		return refund(hotelId, acc_id, accounts);
	}

	/**
	 * 取消
	 */
	@Override
	public CashierPostResp cancel(@NotNull String hotelId, @NotNull String acc_id, String remark) {
		return refund(hotelId, acc_id, null, remark);
//		List<GuestAccounts> orgAccs = guestAccountMapper.getRelationAccounts(hotelId, CollectionUtil.toList(acc_id));
//		if (orgAccs.isEmpty()) {
//			throw new BizException("CANCEL_RES_NOT_FOUND", "客人账目不存在");
//		}
//		Collection<String> resNos = new HashSet<>();
//		List<GuestAccounts> hedges = new ArrayList<>();
//		Map<String, String> ids_hedge = new HashMap<>();
//		GuestAccounts orgAcc = null;
//		for (GuestAccounts org : orgAccs) {
//			// 检查日期，非当日不可操作
//			if (0 != CalculateDate.compareDates(org.getCreateTime(), new Date())) {
//				throw new BizException("CANCEL_DATE_DIFF", "仅可撤销当天发生的账目");
//			}
//			if (StrUtil.equals(org.getAccountsId(), acc_id)) {
//				orgAcc = org;
//			}
//		}
//		for (GuestAccounts org : orgAccs) {
//			resNos.add(org.getReservationNumber());
//			ids_hedge.put(org.getAccountsId(), IdUtil.genStrId());
//			GuestAccounts entity;
//			org.setInternal(true);// 原纪录转内部
//			BeanUtil.copyProperties(org, entity = new GuestAccounts());
//			hedges.add(entity);
//			entity.setQuantity(entity.getQuantity().negate());// 对冲原纪录
//			entity.setCredit(entity.getCredit().negate());
//			org.setRemark(org.getRemark().concat(remark));
//		}
//		// 替换新记录的acc-id
//		Collection<String> payment_ids = new LinkedHashSet<>();
//		hedges.forEach(e -> {
//			e.setAccountsId(ids_hedge.getOrDefault(e.getAccountsId(), ""));
//			e.setParent_id(ids_hedge.getOrDefault(e.getParent_id(), ""));
//			e.setMaster_id(ids_hedge.getOrDefault(e.getMaster_id(), ""));
//			payment_ids.add(e.getAccountsId());
//		});
//		Arrays.asList(orgAccs, hedges).forEach(lst -> {
//			lst.forEach(e -> {
//				guestAccountMapper.save(JpaUtil.appendEntity(e));
//			});
//		});
//		// 对冲代付账
//		orgAccs.forEach(acc -> {
//			if (!StrUtil.isNotBlank(acc.getAr_no())) {
//				ar_write_off(hotelId, acc, payment_ids);
//			}
//		});
//		// 同步预定余额
//		for (String res_no : resNos) {
//			synResBalance(hotelId, res_no);
//		}
	}

	@Override
	public ArAccTransferRes transferArAccBalance(String hotelId, ArAccDepositTransferReq req) {
		if (req.getAmount().compareTo(BigDecimal.ZERO) < 0) {
			throw new BizException("AMOUNT_ILLEGAL", "请正确填写退款金额");
		}
		BigDecimal arBalance = ObjectUtil.defaultIfNull(guestAccountMapper.getArPayAbleBalance(hotelId, req.getArNo()), BigDecimal.ZERO);
		if (arBalance.add(req.getAmount()).compareTo(BigDecimal.ZERO) > 0) {
			throw new BizException("AR_BLC_NOT_ENOUGH", StrUtil.format("账户余额不足, 企业余额: {}", arBalance.negate()));
		}
		// 入正数，用于核销已有企业余额
		CashierPostReq cashierPostReq = new CashierPostReq()//
				.setAr_no(req.getArNo())//
				.setRemark(req.getRemark())//
				.setAccounts(CollectionUtil.toList(new CashierAccountReq()//
						.setDepartmentCode(AccountItemEnum.ARBLCTRSF.getCode())//
						.setDescription("余额转移")//
						.setRemark(StrUtil.format("转至{} {}", req.getTargetArNo(), req.getRemark()).trim())//
						.setCredit(req.getAmount()))//
				)//
		;
		Collection<String> master_ids = arBalanceRefund(hotelId, cashierPostReq).getMaster_ids();
		// 入负数对冲记录用于转移
		cashierPostReq = new CashierPostReq()//
				.setAr_no(req.getArNo())//
				.setRemark(req.getRemark())//
				.setAccounts(CollectionUtil.toList(new CashierAccountReq().setDepartmentCode(AccountItemEnum.ARBLCTRSF.getCode())//
						.setDescription(StrUtil.format("余额转移")).setRemark(StrUtil.format("转自{}", req.getArNo()))//
						.setCredit(req.getAmount().negate()))//
				)//
		;
		Collection<String> trans_ids = post(hotelId, cashierPostReq, acc -> {
			acc.setSplit_from_id(master_ids.iterator().next());
		}).getMaster_ids();
		// 用企业余额核销正数记录
//		ar_write_off(hotelId, new ArWriteOffReq().setAcc_ids(master_ids).setRemark(req.getRemark()).setUse_ar_balance(true));
		// 转移负数记录
		return transferArAccDetails(hotelId, trans_ids, req.getTargetArNo(), req.getRemark());
	}

	@Override
	public ArAccTransferRes transferArAccDetails(@NotNull String hotelId, @NotNull Collection<String> acc_ids, String to_ar_no, String remark) {
		ArAccTransferRes rst = new ArAccTransferRes();
		Collection<String> transfer_ids = new ArrayList<>();
		DefValData<String, ArAmounts> arBals = new DefValData<String, ArAmounts>().setInitHandler(k -> new ArAmounts());
		for (String acc_id : acc_ids) {
			GuestAccounts acc = guestAccountMapper.getGuestAccounts(hotelId, acc_id);
			if (null == acc) {
				rst.getFailed_msg().add(new MessageRsp().setSource_id(acc_id).setCode("AR_ACC_NOT_FND").setMsg("应收帐缺失"));
				continue;
			}
			if (getAmount(acc).add(acc.getAmount_write_off()).compareTo(BigDecimal.ZERO) == 0) {
				rst.getFailed_msg().add(new MessageRsp().setSource_id(acc_id).setCode("AR_ACC_WRITTENOFF").setMsg("应收帐已核销完成"));
				continue;
			}
			transfer_ids.add(acc_id);
		}
		guestAccountMapper.getRelationAccounts(hotelId, acc_ids).forEach(acc -> {
			Arrays.asList(acc.getAr_no(), to_ar_no).forEach(ar_no -> {
				Function<BigDecimal, BigDecimal> transAmt = amt -> StrUtil.equals(to_ar_no, ar_no) ? amt : amt.negate();
				ArAmounts amts = arBals.get(ar_no);
				amts.setBalance(transAmt.apply(acc.getCredit()));
				amts.setCredit(transAmt.apply(acc.getCredit()));
				amts.setNoacc(transAmt.apply(acc.getPrice().multiply(acc.getQuantity())));
			});
		});
		rst.getSuccess_ids().addAll(transfer_ids);
//		Map<String, Object> params = new HashMap<>();
//		params.put("ar_no", to_ar_no);
//		params.put("add_remark", remark);
//		params.put("hotelId", hotelId);
//		params.put("ids", acc_ids);
//		int count = dao.executeUpdate(
//				"UPDATE GuestAccounts set ar_no=:ar_no, remark=SUBSTRING(CONCAT(remark, ' ', :add_remark) , 1, 255) where hotelId=:hotelId and master_id in (:ids)", params);
		List<GuestAccounts> accList = guestAccountMapper.getRelationAccounts(hotelId, acc_ids);
		accList.stream().filter(e -> StrUtil.isNotBlank(e.getAr_no())).forEach(e -> {
			JpaUtil.appendEntity(e);
			e.setAr_no(to_ar_no);
			e.setRemark(StrUtil.concat(true, e.getRemark(), " ", remark).trim());
		});
		guestAccountMapper.saveAll(accList);
		arBals.getDataMap().forEach((ar_no, amts) -> incArBalance(hotelId, ar_no, amts));
		return rst;
	}

	@Override
	public void ar_write_off(@NotNull String hotelId, @NotNull ArWriteOffReq req) {
		List<GuestAccounts> accounts = guestAccountMapper.getRelationAccounts(hotelId, req.getAcc_ids());
		Map<String, GuestAccounts> idAccs = new HashMap<>();
		Set<String> ar_nos = new HashSet<>();
		BigDecimal total_acc = BigDecimal.ZERO, total_payment = BigDecimal.ZERO;
		for (GuestAccounts acc : accounts) {
			ar_nos.add(acc.getAr_no());
			total_acc = getAmount(acc).add(total_acc);
			idAccs.put(acc.getAccountsId(), acc);
		}
		String ar_no;
		if (ar_nos.isEmpty()) {
			throw new BizException("ACC_NOT_FOUND", "核销账不存在");
		} else if (ar_nos.size() != 1) {
			throw new BizException("MUL_AR_NO", "存在多个核销账户");
		} else {
			ar_no = ar_nos.iterator().next();
		}
		for (String id : req.getAcc_ids()) {
			GuestAccounts acc = idAccs.get(id);
			if (null == acc) {
				throw new BizException("ACC_LOST", "应收帐缺失");
			}
			if (getAmount(acc).add(acc.getAmount_write_off()).compareTo(BigDecimal.ZERO) == 0) {
//			if (ObjectUtil.defaultIfNull(guestAccountMapper.getArAccBalance(hotelId, id), total_acc).compareTo(BigDecimal.ZERO) == 0) {
				throw new BizException("BLC_ERR", "撤销已完成");
			}
		}
		if (null != req.getDiscounts()) {
			List<CashierAccountReq> payments = new ArrayList<>();
			for (ArDiscountReq discountReq : req.getDiscounts()) {// 优先使用折扣
				CashierAccountReq accountReq = new CashierAccountReq();
				accountReq.setAr_no(ar_no);
				accountReq.setCredit(discountReq.getAmount().negate());
				accountReq.setPrice(discountReq.getAmount());
				accountReq.setQuantity(BigDecimal.ONE.negate());
				accountReq.setDepartmentCode(discountReq.getDepartmentCode());
				accountReq.setRemark(discountReq.getRemark());
				payments.add(accountReq);
			}
			payments.addAll(req.getPayments());
			req.setPayments(payments);
		}
		for (CashierAccountReq payment_req : req.getPayments()) {
			formatCashierAccountReq(hotelId, payment_req);
			if (payment_req.getPrice().compareTo(BigDecimal.ZERO) < 0) {
				throw new BizException("AMOUNT_ILLEGAL", "金额非法：折扣金额不能为负数");
			} else if (payment_req.getCredit().compareTo(BigDecimal.ZERO) > 0) {
				throw new BizException("AMOUNT_ILLEGAL", "金额非法：付款金额不能为正数");
			}
			total_payment = payment_req.getPrice().multiply(payment_req.getQuantity()).add(payment_req.getCredit()).add(total_payment);
		}
		// 1, 检验余额是否匹配
		if (total_acc.add(total_payment).compareTo(BigDecimal.ZERO) > 0) {// 输入金额不足以完全冲销代核销账目
			if (req.isUse_ar_balance()) {// 如果使用户余额，检测账户余额是否充足
				BigDecimal total_arblc = ObjectUtil.defaultIfNull(guestAccountMapper.getArPayAbleBalance(hotelId, ar_no), BigDecimal.ZERO);
				BigDecimal diff = total_acc.add(total_payment).add(total_arblc);
				if (diff.compareTo(BigDecimal.ZERO) > 0) {
					throw new BizException("AR_BLC_NOT_ENOUGH", StrUtil.format("账户余额不足, 企业余额: {}", total_arblc.negate()));
				}
			} else {
				throw new BizException("AR_WRITE_OFF_AMT_NOT_ENOUGH", "金额不足以完成核销");
			}
		}
		// 参与对冲的付款记录ID
		Collection<String> payment_ids = new LinkedHashSet<>();
		{// 入账
			CashierPostReq cashierPostReq = new CashierPostReq();
			cashierPostReq.setReservationNumber("");
			cashierPostReq.setAr_no(ar_no);
			cashierPostReq.setRemark(req.getRemark());
			cashierPostReq.setAccounts(req.getPayments());
			CashierPostResp postResp = post(hotelId, cashierPostReq);
			payment_ids.addAll(postResp.getMaster_ids());
		}
		if (req.isUse_ar_balance()) {// 使用账户余额
			payment_ids.addAll(guestAccountMapper.getArPayAbleIDs(hotelId, ar_no));
		}
		payment_ids.removeAll(req.getAcc_ids());// 排除自身
		ArAmounts synBalance = new ArAmounts();
		for (GuestAccounts acc : accounts) {
			ar_write_off(hotelId, acc, payment_ids);
			if (StrUtil.isNotBlank(acc.getRes_no_org())) {// 预定挂账才进入累计
				synBalance.setWriteoff(getAmount(acc).add(synBalance.getWriteoff()));
			}
			if (acc.getCredit().compareTo(BigDecimal.ZERO) == 0) {// 退款模式下不需要同步可用余额
				synBalance.setBalance(
						ObjectUtil.defaultIfNull(guestAccountMapper.getArPayforCreditAmount(hotelId, acc.getMaster_id()), BigDecimal.ZERO).negate().add(synBalance.getBalance()));
			}
		}
		synBalance.setNoacc(total_acc.negate());// 但是待核销会处理一些异常数据，因此为总数取反
//		synArBalance(hotelId, ar_no, true);
		incArBalance(hotelId, ar_no, synBalance);
	}

	@Override
	public void ar_write_off(@NotNull String hotelId, GuestAccounts acc, Collection<String> payment_ids) {
		payment_ids.remove(acc.getAccountsId());// 排除自身
		BigDecimal amt_write_off = getAmount(acc).add(acc.getAmount_write_off());// guestAccountMapper.getArAccBalance(hotelId, acc.getMaster_id());
		if (amt_write_off.compareTo(BigDecimal.ZERO) == 0) {
			return;
		}
		Collection<String> remove_ids = new HashSet<>();
		for (String payment_acc_id : payment_ids) {//
			BigDecimal amt_payment = ObjectUtil.defaultIfNull(guestAccountMapper.getAccBalance(hotelId, payment_acc_id), BigDecimal.ZERO);
			if (0 == amt_payment.compareTo(BigDecimal.ZERO)) {
				remove_ids.add(payment_acc_id);
				continue;
			}
			if (amt_payment.add(amt_write_off).compareTo(BigDecimal.ZERO) < 0) {// 如果付款余额冲销待支付后仍有余额
				// 则拆分付款使待支付完成冲销，使用拆分出的新ID进行冲销
				payment_acc_id = split(hotelId, payment_acc_id, amt_write_off.negate(), StrUtil.format("核销({})", acc.getAr_no()));
				amt_payment = amt_write_off.negate();// 付款金额为完全冲销金额
			} else {// 否则则为付款完全冲销于待支付，则不参与后期其他待支付冲销
				remove_ids.add(payment_acc_id);
				GuestAccounts payment = guestAccountMapper.getGuestAccounts(hotelId, payment_acc_id);
				if (0 != DateUtil.compare(payment.getCreateTime(), DateUtil.date(), "yyyyMMdd")) {// 非同一日的分离，保留日期（当日核销用CreateTime）
//				payment_acc_id = split(hotelId, payment_acc_id, amt_write_off.negate(), StrUtil.format("拆分: {}/{}", amt_write_off.negate(), amt_payment));
					payment_acc_id = split(hotelId, payment_acc_id, amt_payment, StrUtil.format("核销({})", acc.getAr_no()));
				}
			}
//			guestAccountMapper.arWriteOff(hotelId, acc.getAccountsId(), payment_acc_id);// 执行冲销操作
			List<GuestAccounts> accList = guestAccountMapper.getRelationAccounts(hotelId, Arrays.asList(payment_acc_id));
			accList.stream().filter(e -> StrUtil.equals(e.getAr_no(), acc.getAr_no())).forEach(e -> {
				JpaUtil.appendEntity(e);
				e.setAr_payfor_id(acc.getAccountsId());
			});
			guestAccountMapper.saveAll(accList);
			amt_write_off = amt_write_off.add(amt_payment);// 检测是否完全冲销完，仍有余额则继续执行下个循环
			acc.setAmount_write_off(acc.getAmount_write_off().add(amt_payment));
			if (amt_write_off.compareTo(BigDecimal.ZERO) == 0) {
				break;// 无余额，冲销完成
			}
		}
		guestAccountMapper.saveAndFlush(acc);
		payment_ids.removeAll(remove_ids);
		log.info("应收核销({}) {} - {} 金额: {}", acc.getDepartmentCode(), acc.getDescription(), acc.getAccountsId(), acc.getAmount_write_off());
	}

	@Override
	public void arCancelWriteOff(String hotelId, String acc_id, String remark) {
		List<GuestAccounts> orgs = guestAccountMapper.getArWriteOffDetails(hotelId, acc_id);
		if (CollectionUtil.isEmpty(orgs)) {
			orgs = guestAccountMapper.getArWriteOffDetails(hotelId, guestAccountMapper.getGuestAccounts(hotelId, acc_id).getMaster_id());
		}
		BigDecimal total = BigDecimal.ZERO;
		Set<String> ar_nos = new HashSet<>();
		for (GuestAccounts acc : orgs) {
			ar_nos.add(acc.getAr_no());
			total = getAmount(acc).add(total);
		}
		if (total.compareTo(BigDecimal.ZERO) == 0) {
			throw new BizException("BLC_ERR", "撤销已完成");
		}
		String ar_no;
		if (ar_nos.isEmpty()) {
			throw new BizException("ACC_NOT_FOUND", "核销账不存在");
		} else if (ar_nos.size() != 1) {
			throw new BizException("MUL_AR_NO", "存在多个核销账户");
		} else {
			ar_no = ar_nos.iterator().next();
		}
		// 记录之前已撤销的记录
		Collection<String> canceledIds = new HashSet<>();
		orgs.stream().filter(e -> !StrUtil.equals(e.getAccountsId(), e.getMaster_id())).forEach(e -> {
			canceledIds.add(e.getAccountsId());
			canceledIds.add(e.getMaster_id());
		});

		List<GuestAccounts> orgsFilted = new ArrayList<>();
		orgs.stream().filter(e -> !CollectionUtil.contains(canceledIds, e.getAccountsId())).forEach(orgsFilted::add);
//		if (true) {
//			throw new BizException("BLC_ERR", "已撤销筛选待处理" + canceledIds + ":" + orgsFilted.size());
//		}
		// 对冲帐调原有记录
		List<GuestAccounts> negates = copyCreateAccounts(orgsFilted, (orgEntity, newEntity) -> {
			newEntity.setBusiness_date(LocalDate.now());
			newEntity.setRemark(remark);
			newEntity.setAr_payfor_id(acc_id);// 核销采用update方式更新，如果是同一事务会导致本帐刷新不实时（test）这里直接复制，反正要复制的也是这个，安全
			newEntity.setPrice(orgEntity.getPrice().negate());
			newEntity.setCredit(orgEntity.getCredit().negate());
			newEntity.setMaster_id(orgEntity.getMaster_id());
			return true;
		});
		// 复制帐回复可支付
		List<GuestAccounts> copyNews = copyCreateAccounts(orgsFilted, (orgEntity, newEntity) -> {
			newEntity.setBusiness_date(LocalDate.now());
			newEntity.setRemark(remark);
			newEntity.setAr_payfor_id(null);
			newEntity.setPrice(orgEntity.getPrice());
			newEntity.setCredit(orgEntity.getCredit());
			// 坏账和折扣只针对本次核销，不返还企业余额
			return newEntity.getCredit().compareTo(BigDecimal.ZERO) != 0;
		});
		List<GuestAccounts> updates = new ArrayList<>();
		ArAmounts blcs = new ArAmounts();
		negates.forEach(e -> {
			updates.add(JpaUtil.appendEntity(e));
			if (0 == e.getCredit().compareTo(BigDecimal.ZERO)) {
				if (StrUtil.isBlank(e.getRes_no_org())) {
					blcs.setAdjust(blcs.getAdjust().add(getAmount(e)));
				}
			} else {
				blcs.setBalance(blcs.getBalance().subtract(getAmount(e)));
			}
		});
		blcs.setWriteoff(total);
		blcs.setNoacc(total.negate());
		copyNews.forEach(e -> {
			updates.add(JpaUtil.appendEntity(e));
//			if (0 == e.getCredit().compareTo(BigDecimal.ZERO)) {
//				if (StrUtil.isBlank(e.getRes_no_org())) {
//					blcs.setAdjust(blcs.getAdjust().add(getAmount(e)));
//				}
//			} else {
//				blcs.setBalance(blcs.getBalance().add(getAmount(e)));
//			}
		});
		guestAccountMapper.saveAll(updates);
		GuestAccounts acc = guestAccountMapper.getGuestAccounts(hotelId, acc_id);
		acc.setAmount_write_off(acc.getAmount_write_off().subtract(total));
		guestAccountMapper.saveAndFlush(acc);
//		synArBalance(hotelId, ar_no, true);
		incArBalance(hotelId, ar_no, blcs);
	}

	@Override
	public CashierPostResp arBalanceRefund(String hotelId, CashierPostReq req) {
		if (StrUtil.isNotBlank(req.getReservationNumber())) {
			throw new BizException("ARGS_ILLEGAL", "企业退款模式下不允许指定预定");
		}
		if (StrUtil.isBlank(req.getAr_no())) {
			throw new BizException("ARGS_ILLEGAL", "企业退款模式下需指定账户");
		}
		BigDecimal total = BigDecimal.ZERO;
		for (CashierAccountReq accountReq : req.getAccounts()) {
			formatCashierAccountReq(hotelId, accountReq);
			total = accountReq.getPrice().multiply(accountReq.getQuantity()).add(accountReq.getCredit()).add(total);
		}
		if (total.compareTo(BigDecimal.ZERO) < 0) {
			throw new BizException("AMOUNT_ILLEGAL", "请正确填写退款金额");
		}
		BigDecimal arBalance = ObjectUtil.defaultIfNull(guestAccountMapper.getArPayAbleBalance(hotelId, req.getAr_no()), BigDecimal.ZERO);
		if (arBalance.add(total).compareTo(BigDecimal.ZERO) > 0) {
			throw new BizException("AR_BLC_NOT_ENOUGH", StrUtil.format("账户余额不足, 企业余额: {}", arBalance.negate()));
		}
		CashierPostResp rst = post(hotelId, req);
		ArWriteOffReq writeOffReq = new ArWriteOffReq();
		writeOffReq.setAcc_ids(rst.getAcc_ids()).setRemark(req.getRemark());
		writeOffReq.setDiscounts(new ArrayList<>());
		writeOffReq.setPayments(new ArrayList<>());
		writeOffReq.setUse_ar_balance(true);
		ar_write_off(hotelId, writeOffReq);
		return rst;
	}

	@Override
	public ArAgingAmountsResp getArAgingAmount(String hotelId, String ar_no) {
		ArAgingAmountsResp resp = new ArAgingAmountsResp();
		ArAccount cust = araccountMapper.findByHotelIdAndCode(hotelId, ar_no);
		if (1 == cust.getSettlecycle()) {
			java.time.LocalDate e1 = cust.getSettleday() < 1 ? LocalDate.now().withDayOfMonth(1).plusDays(-1) : LocalDate.now().plusMonths(-1).withDayOfMonth(cust.getSettleday());
			java.time.LocalDate e2 = cust.getSettleday() < 1 ? LocalDate.now().plusMonths(-1).withDayOfMonth(1).plusDays(-1) : e1.plusMonths(-1);
			java.time.LocalDate e3 = cust.getSettleday() < 1 ? LocalDate.now().plusMonths(-2).withDayOfMonth(1).plusDays(-1) : e2.plusMonths(-1);
			java.time.LocalDate f1 = e2.plusDays(1);
			java.time.LocalDate f2 = e3.plusDays(1);
			resp.getDetails().add(new ArAgingAmountDetail().setDescription(f1 + " 至 " + e1).setAmount(guestAccountMapper.getArOpenBalance(hotelId, ar_no, f1, e1)));
			resp.getDetails().add(new ArAgingAmountDetail().setDescription(f2 + " 至 " + e2).setAmount(guestAccountMapper.getArOpenBalance(hotelId, ar_no, f2, e2)));
			resp.getDetails().add(new ArAgingAmountDetail().setDescription(e3 + "(含)之前").setAmount(guestAccountMapper.getArOpenBalance(hotelId, ar_no, e3)));
		}
		return resp;
	}
}
