package com.cw.service.config.reservation;

import com.cw.pojo.dto.common.req.CommonQueryReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.Common_Price_Req;
import com.cw.pojo.dto.common.res.Common_Price_Res;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.reservation.GroupRoomSearchReq;
import com.cw.pojo.dto.pms.req.reservation.Ratequery_List_Req;
import com.cw.pojo.dto.pms.req.room.RoomSearchReq;
import com.cw.pojo.dto.pms.res.reservation.GroupRoomSearchRes;
import com.cw.pojo.dto.pms.res.reservation.PkgQueryRes;
import com.cw.pojo.dto.pms.res.reservation.Ratequery_List_Res;
import com.cw.pojo.dto.pms.res.reservation.ReservationRes;
import com.cw.pojo.dto.pms.res.room.RoomListRes;

import java.util.List;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/6/13 15:43
 **/
public interface RsInfoService {


    Common_Price_Res calcPrice(Common_Price_Req req);

    Common_response validateRoomOcc(CommonQueryReq req);

    ReservationRes queryOccRoom(CommonQueryReq req);

    GroupRoomSearchRes queryGroupRes(GroupRoomSearchReq req);

    Ratequery_List_Res getRpqueryTable(Ratequery_List_Req req);

    PkgQueryRes queryPkg(CommonQueryReq req);

    RoomListRes getAvlRoomPage(RoomSearchReq req);

    Common_response checkAvl(CommonQueryReq req);


}
