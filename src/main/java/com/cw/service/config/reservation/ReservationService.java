package com.cw.service.config.reservation;

import com.cw.entity.Reservation;
import com.cw.exception.DefinedException;
import com.cw.pojo.dto.common.req.CommonQueryPageReq;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.res.Common_response;
import com.cw.pojo.dto.pms.req.reservation.*;
import com.cw.pojo.dto.pms.req.standardgroup.QueryStandardGroupReq;
import com.cw.pojo.dto.pms.req.standardgroup.StandardGroupReq;
import com.cw.pojo.dto.pms.res.profile.ProfileRes;
import com.cw.pojo.dto.pms.res.rate.RoomRateDetailRes;
import com.cw.pojo.dto.pms.res.reservation.*;
import com.cw.pojo.dto.pms.res.standardgroup.StandardGroupRes;

import java.util.List;

/**
 * @Classname ReservationService
 * @Description 预定信息接口
 * @Date 2024-03-27 20:44
 * <AUTHOR> sancho.shen
 */
public interface ReservationService {
    RsOrderResult addReservationTourist(ReservationReq reservationReq) throws DefinedException;

    RsOrderResult addGroupReservation(BatchCreateRsReq reservationReq) throws DefinedException;

    void updateReservation(UpdateReservationReq updateReservationReq) throws DefinedException;

    ReservationListRes listReservationPage(QueryReservationListReq queryReservationListReq);

    ReservationListRes queryTransRes(QueryRsTransferPageReq queryReservationListReq);

    void cancelReservation(Common_Load_Req req) throws DefinedException;

    ReservationRes queryRoomRs(QueryReservationReq req);

    List<ReservationRes> listReservation(String relationNumber);

    List<ReservationRes> queryReservation(QueryReservationReq queryReservationReq);

    RoomRsViewRes loadRoomRsView(RoomRsLoadReq req);

    void deleteReservation(Long id);

    void checkin(CheckinReq checkinReq);

    void cancel(CancelReq cancelReq) throws DefinedException;

    void cancelCheckin(CancelReq cancelReq) throws DefinedException;

    void roomMove(RoomMoveReq roomMoveReq) throws DefinedException;

    void checkout(CheckoutReq checkoutReq) throws DefinedException;

    Common_response batchCheckout(BatchCheckoutReq batchCheckoutReq) throws DefinedException;

    void addShare(AddShareReq addShareReq);

    void extend(ExtendReq extendReq)throws DefinedException;

    RateQueryRes rateQuery(RateQueryReq rateQueryReq);

    RoomRateDetailRes queryRoomRate(QueryRateReq queryRateReq);

    AssignReservationRes assignRoom(AssignRoomReq assignRoomReq);

    BatchAssginRoomResult batchAssignRoom(BatchAssignRoomReq batchAssignRoomReq) throws DefinedException;

    GroupRsListRes listReservationGroup(QueryStandardGroupReq queryStandardGroupReq);

    void registration(RegistrationReq registrationReq);


    List<ProfileRes> findProfile(String  reservationNumber);

    Reservation internalReactiveReservation(String hotelId, String orderNo, String userId) throws DefinedException;

    void transferGroup(GroupTransferReq req) throws DefinedException;

    RsOrderResult copyReservation(CopyReservationReq req) throws DefinedException;

    void pendingCheckOut(PendingCheckOutReq pendingCheckOutReq) throws DefinedException;

    Reservation cancelPending(String hotelId, String orderNo, String userId) throws DefinedException;

    void cancelCheckout(CancelReq cancelReq) throws DefinedException;

    CheckinVerificationRes checkinVerification(CheckinVerificationReq req);
}
