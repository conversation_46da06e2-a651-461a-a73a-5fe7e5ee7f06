package com.cw.service.config.market.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.cw.cache.GlobalCache;
import com.cw.config.exception.BizException;
import com.cw.core.SeqNoService;
import com.cw.entity.Market;
import com.cw.mapper.MarketMapper;
import com.cw.pojo.dto.pms.req.market.MarketReq;
import com.cw.pojo.dto.pms.req.market.QueryMarketReq;
import com.cw.pojo.dto.pms.req.market.UpdateMarketReq;
import com.cw.pojo.dto.pms.res.market.MarketListRes;
import com.cw.service.config.market.MarketService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.JpaUtil;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.jpa.DynamicSpecificationBuilder;
import com.cw.utils.jpa.Operator;
import com.cw.utils.jpa.SearchCriteria;
import com.cw.utils.pages.PageResUtil;
import com.google.common.collect.Lists;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 市场接口实现类
 * @Author: michael.pan
 * @Date: 2024/3/23 22:11
 */
@Service
public class MarketServiceImpl implements MarketService {

    @Resource
    private MarketMapper marketMapper;
    

    @Override
    public void addMarket(MarketReq marketReq) {
        String currentHotelId = GlobalContext.getCurrentHotelId();
        List<String> descriptions = Lists.newArrayList(marketReq.getDescription());
        StringBuilder liveDescriptionsSb = new StringBuilder();

        for(String desc : descriptions){
            int characterSize = marketMapper.countByHotelIdAndDescription(currentHotelId, desc);
            //校验该市场是否存在,如果存在则，先记录出来，不影响其他正常的市场保存，后面再统一将错误的抛错出来
            if(characterSize > 0){
                liveDescriptionsSb.append(desc);
                liveDescriptionsSb.append("、");
                continue;
            }
            
            Market market = new Market();
            BeanUtil.copyProperties(marketReq, market);
            market.setHotelId(currentHotelId);
            market.setCode(SeqNoService.getInstance().getUniqueCode(desc, market.getHotelId(), GlobalDataType.MARKET));
            market.setDescription(desc);
            marketMapper.save(JpaUtil.appendEntity(market));
        }
        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.MARKET, currentHotelId);

        String liveDescriptions = liveDescriptionsSb.toString();
        //不影响其他正常的市场保存，统一将错误的抛错出来
        if(StrUtil.isNotEmpty(liveDescriptions)){
            //去掉最后一个分号（、）
            StringBuilder replaceMsg = liveDescriptionsSb.replace(liveDescriptions.length() - 1, liveDescriptions.length(), "");
            throw new BizException(StrUtil.format("市场为{}已存在，无法重复添加！", replaceMsg));
        }
    }

    @Override
    public void updateMarket(UpdateMarketReq marketReq) {
        // 先查询
        Optional<Market> data = marketMapper.findById(marketReq.getId());
        String currentHotelId = GlobalContext.getCurrentHotelId();
        String description = marketReq.getDescription();
        //市场重复性校验
        int marketSize = marketMapper.countByHotelIdAndDescription(currentHotelId, description);
        if (!data.get().getDescription().equalsIgnoreCase(description) && marketSize > 0) {
            //不允许修改成已经存在的房间号
            throw new BizException(StrUtil.format("该市场{}已存在，请确认后重试", description));
        }

        marketMapper.save(JpaUtil.appendEntity(data, marketReq));
        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.MARKET, currentHotelId);
    }

    @Override
    public MarketListRes listMarket(QueryMarketReq queryMarketReq) {
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("description", queryMarketReq.getDescription(), Operator.LIKE));
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", GlobalContext.getCurrentHotelId(), Operator.EQUAL));
        Specification<Market> querySpecification = DynamicSpecificationBuilder.getQuerySpecification(Market.class, conditionList);
        Page<Market> marketPage = marketMapper.findAll(querySpecification, JpaUtil.getPageRequest(queryMarketReq.getPages()));
        return PageResUtil.fillPagesDate(marketPage, MarketListRes.class);
    }

    @Override
    public void deleteMarket(Long id) {
        marketMapper.deleteById(id);

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.MARKET, GlobalContext.getCurrentHotelId());
    }
}
