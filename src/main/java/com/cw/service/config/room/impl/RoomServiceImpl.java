package com.cw.service.config.room.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cw.cache.CustomData;
import com.cw.cache.GlobalCache;
import com.cw.cache.impl.FloorCache;
import com.cw.cache.impl.RoomTypeCache;
import com.cw.config.exception.BizException;
import com.cw.entity.*;
import com.cw.mapper.*;
import com.cw.mapper.common.DaoLocal;
import com.cw.pojo.dto.common.req.Common_Load_Req;
import com.cw.pojo.dto.common.req.PageReq;
import com.cw.pojo.dto.pms.req.reservation.Accompany;
import com.cw.pojo.dto.pms.req.room.*;
import com.cw.pojo.dto.pms.res.room.*;
import com.cw.pojo.sqlresult.RroomsAvlPo;
import com.cw.service.config.room.RoomService;
import com.cw.service.context.GlobalContext;
import com.cw.utils.CalculateDate;
import com.cw.utils.JpaUtil;
import com.cw.utils.SystemUtil;
import com.cw.utils.annotion.LockOp;
import com.cw.utils.enums.GlobalDataType;
import com.cw.utils.enums.RoomStateEnum;
import com.cw.utils.enums.StatusTypeUtils;
import com.cw.utils.jpa.DynamicSpecificationBuilder;
import com.cw.utils.jpa.Operator;
import com.cw.utils.jpa.SearchCriteria;
import com.cw.utils.pages.PageResUtil;
import com.google.common.collect.*;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 房间接口实现类
 * @Author: michael.pan
 * @Date: 2024/3/20 22:26
 */
@Slf4j
@Service
public class RoomServiceImpl implements RoomService {
    @Resource
    private RoomMapper roomMapper;
    @Resource
    private RoomTypeMapper roomTypeMapper;
    @Resource
    private RoomQuantityMapper roomQuantityMapper;
    @Resource
    private ReservationMapper reservationMapper;

    @Autowired
    private DaoLocal<?> daoLocal;
    @Resource
    private RmstatMapper rmstatMapper;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRoom(RoomInfoReq roomInfoReq) {
        String currentHotelId = GlobalContext.getCurrentHotelId();
        String postRoomType = roomInfoReq.getRoomType();
        //查询房型，如果不存在该房型，则直接抛错
        RoomType roomType = findRoomType(currentHotelId, postRoomType);

        List<String> roomNos = roomInfoReq.getRoomNos();
        // 批量检查房间是否已存在并添加新房间
        List<Room> roomsToSave = new ArrayList<>();
        for (String roomNo : roomNos) {
            if (isRoomExists(currentHotelId, roomNo)) {
                throw new BizException(StrUtil.format("房间{}已经存在，不能重复添加", roomNo));
            }
            Room roomEntity = new Room();
            BeanUtil.copyProperties(roomInfoReq, roomEntity);
            roomEntity.setHotelId(currentHotelId);
            roomEntity.setRoomNo(roomNo);
            roomEntity.setRoomStatus(StatusTypeUtils.RoomStatus.CLEAN);
            roomEntity.setLocc(0);
            roomEntity.setRoomType(roomType.getRoomType());
            //roomEntity.setDescription(roomType.getDescription());
            roomsToSave.add(JpaUtil.appendEntity(roomEntity));
        }
        List<Room> roomsList = roomMapper.findAllByHotelIdAndSuirooms(GlobalContext.getCurrentHotelId());
        // 已选的套房物理房集合
        List<String> roomSuiteList =  roomsList.stream()
                .map(room -> room.getSuirooms())
                .filter(StrUtil::isNotEmpty) // 过滤掉空字符串
                .flatMap(suirooms -> Arrays.stream(suirooms.split(",")))
                .map(String::trim) // 去除元素前后的空格
                .collect(Collectors.toList());
        String[] rooms = roomInfoReq.getSuirooms().split(",");
        Map<String, Room> roomMap = roomMapper.findByHotelIdAndLocc(GlobalContext.getCurrentHotelId(), StatusTypeUtils.RoomLocc.OCCUPY).stream()
                .collect(Collectors.toMap(Room::getRoomNo, room -> room));
        for (String roomNo : rooms) {
            if (roomMap.get(roomNo) != null) {
                throw new BizException(StrUtil.format("房间{}已被占用，请重新选择", roomNo));
            }
            if (roomSuiteList.contains(roomNo)) {
                throw new BizException(StrUtil.format("房间{}已被其他套房选中，不能重复添加", roomNo));
            }
        }

        //批量新增房间
        roomMapper.saveAll(roomsToSave);
        //更新对应房型的房间数量
        updateRoomTypeRoomNumber(roomType, roomNos.size());
        //更新对应的可卖房量数据
        updateRoomQuantity(currentHotelId, roomType.getRoomType(), roomNos.size());

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOM, GlobalContext.getCurrentHotelId());
        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOMTYPE, currentHotelId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRoom(UpdateRoomInfoReq roomInfoReq) {
        // 先查询
        Optional<Room> data = roomMapper.findById(roomInfoReq.getId());
        String roomNo = roomInfoReq.getRoomNo();
        if (!data.isPresent()) {
            throw new BizException(StrUtil.format("房间{}不存在，请确认后重试",roomNo));
        }
        Room room = data.get();
        String currentHotelId = GlobalContext.getCurrentHotelId();
        String postRoomType = roomInfoReq.getRoomType();
        String dbRoomType = room.getRoomType();
        //根据房型名称查询对应的房间
        RoomType roomType = roomTypeMapper.findByHotelIdAndRoomType(currentHotelId, postRoomType);
        if (ObjectUtils.isEmpty(roomType)) {
            throw new BizException("该房型不存在，请在房型管理处添加该房型");
        }
        //如果修改的房间号预原房间号不一致，则需要校验新的房间号是否存在
        int roomSize = roomMapper.countByHotelIdAndRoomNo(currentHotelId, roomNo);
        if (!room.getRoomNo().equalsIgnoreCase(roomInfoReq.getRoomNo()) && roomSize > 0) {
            //不允许修改成已经存在的房间号
            throw new BizException(StrUtil.format("该房间{}已存在，请确认后重试", roomNo));
        }
        //更新房间数据 对应修改名称
        //room.setDescription(roomType.getDescription());
        roomMapper.save(JpaUtil.appendEntity(data, roomInfoReq));

        List<Room> roomList = roomMapper.findAllByHotelIdAndRoomType(currentHotelId, postRoomType);
        //更新对应的房型数据
        roomType.setRoomNumber(roomList.size());
        roomTypeMapper.save(roomType);
        //如果更改了房间的房型 需要更新对应的房型数据
        if (!dbRoomType.equals(postRoomType)) {
            RoomType dbRmType = roomTypeMapper.findByHotelIdAndRoomType(currentHotelId, dbRoomType);
            List<Room> dbRoomList = roomMapper.findAllByHotelIdAndRoomType(currentHotelId, dbRoomType);
            dbRmType.setRoomNumber(dbRoomList.size()); //TODO  搞个update 更新语句不就行了吗
            roomTypeMapper.save(dbRmType);
        }

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOM, currentHotelId);
    }

    /**
     * 操作（修改）房态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void operateRoomState(OperateRoomStateReq roomStatueReq) {
        List<String> roomNos = roomStatueReq.getRoomNos();
        String currentHotelId = GlobalContext.getCurrentHotelId();
        String roomStatus = roomStatueReq.getRoomStatus();
        //维修房态
        String oo = StatusTypeUtils.RoomStatus.OO;
        DateTime startDate = DateUtil.beginOfDay(new Date());
        DateTime endTimeDate = DateUtil.beginOfDay(new Date());

        List<Room> rooms = roomMapper.findByRoomNos(roomNos, currentHotelId);
        for (Room room : rooms) {
           // 避免循环查库
//        for (String roomNo : roomNos) {
//            Room room = roomMapper.findRoomByHotelIdAndRoomNo(currentHotelId, roomNo);
//            if (ObjectUtils.isEmpty(room)) {
//                throw new BizException(StrUtil.format("房间{}不存在，请确认后重试", roomNo));
//            }
            //原有的房态
            String oldRoomStatus = room.getRoomStatus();
            int ooo = 0;
            if (oo.equals(roomStatus)) {
//                handleOORoomState(room, roomStatueReq, currentHotelId, oo, ooo);
                String startTime = roomStatueReq.getStartTime();
                String endTime = roomStatueReq.getEndTime();
                startDate = DateUtil.parse(startTime, "yyyy-MM-dd");
                endTimeDate = DateUtil.parse(endTime, "yyyy-MM-dd");
                //前端操作设置该房为OO房，则房量表的ooo+1
                ooo++;
            } else {
                //前端操作取消OO房，且原有的房态为OO，则房量表的ooo量-1,
                if (oo.equals(oldRoomStatus)) {
                    startDate = DateUtil.parse(room.getStartTime(), "yyyy-MM-dd");
                    endTimeDate = DateUtil.parse(room.getEndTime(), "yyyy-MM-dd");
                    ooo--;
                }
                //除了OO房，其他房态的维修开始、结束时间都为空
                room.setStartTime("");
                room.setEndTime("");
                room.setRoomStatus(StatusTypeUtils.RoomStatus.DIRTY);//OO 房 维修完默认变成脏房

            }

            //更新对应房型的房量表
            roomQuantityMapper.updateRroomsOoo(ooo, currentHotelId, room.getRoomType(),startDate, endTimeDate);
            //更新房间信息
            roomMapper.save(JpaUtil.appendEntity(room, roomStatueReq));
        }
        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOM, currentHotelId);
    }

    private void handleOORoomState(Room room, OperateRoomStateReq roomStatueReq, String currentHotelId, String status,int ooo) {
                for (OperateRoomStateReq.dateRange  dateRange : roomStatueReq.getDateRanges()) {
                    DateTime startDate = DateUtil.parse(dateRange.getStartTime(), "yyyy-MM-dd");
                    DateTime endTimeDate = DateUtil.parse(dateRange.getEndTime(), "yyyy-MM-dd");
                    ooo++;
                    roomQuantityMapper.updateRroomsOoo(ooo, currentHotelId, room.getRoomType(),startDate, endTimeDate);

                    rmstatMapper.save(new Rmstat()
                                    .setHotelId(currentHotelId)
                                    .setRoomNo(room.getRoomNo())
                                    .setFromDate(startDate)
                                    .setToDate(endTimeDate)
                                    .setStatus(status)
                            );
                }
    }

    @Override
    public CurrentRoomStateRes listCurrentRoomStatesData(QueryOperationRoomStatedataReq queryOperationRoomStatedataReq) {
        String currentHotelId = GlobalContext.getCurrentHotelId();
        CurrentRoomStateRes statusRes = new CurrentRoomStateRes();
        String clean = StatusTypeUtils.RoomStatus.CLEAN;
        String dirty = StatusTypeUtils.RoomStatus.DIRTY;
        String oo = StatusTypeUtils.RoomStatus.OO;
        String buildingNo = queryOperationRoomStatedataReq.getBuildingNo();
        ArrayList<CurrentRoomStateData> stateList = Lists.newArrayList();

        if (StringUtil.isNotEmpty(buildingNo)) {
            RoomTypeCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
            List<RoomType> roomTypes = cache.getDataList(GlobalContext.getCurrentHotelId());

            // 过滤需要查询楼栋的房型
            List<RoomType> roomTypeList = roomTypes.stream().filter(roomType -> Objects.equals(roomType.getBuildingNo(), buildingNo)).collect(Collectors.toList());
            List<String> roomTypeDataList = roomTypeList.stream().map(roomType -> roomType.getRoomType()).collect(Collectors.toList());
            stateList.add(getCurrentRoomStateData(clean, RoomStateEnum.getDescription(clean), roomMapper.findAllByHotelIdAndRoomStatusAndRoomType(roomTypeDataList,currentHotelId, clean).size(),3));

            stateList.add(getCurrentRoomStateData(dirty, RoomStateEnum.getDescription(dirty), roomMapper.findAllByHotelIdAndRoomStatusAndRoomType(roomTypeDataList,currentHotelId, dirty).size(),3));

            stateList.add(getCurrentRoomStateData(oo, RoomStateEnum.getDescription(oo), roomMapper.findAllByHotelIdAndRoomStatusAndRoomType(roomTypeDataList,currentHotelId, oo).size(),3));

            for (RoomType roomType : roomTypeList) {
                stateList.add(getCurrentRoomStateData(roomType.getRoomType(), roomType.getDescription(),roomType.getRoomNumber(),2));
            }

            stateList.add(getCurrentRoomStateData("1", "占用",roomMapper.findAllByHotelIdAndLoccAndRoomType(roomTypeDataList, currentHotelId, StatusTypeUtils.RoomLocc.OCCUPY).size(),1));
        } else {
            stateList.add(getCurrentRoomStateData(clean, RoomStateEnum.getDescription(clean), roomMapper.countByHotelIdAndRoomStatus(currentHotelId, clean),3));
            //脏房
            stateList.add(getCurrentRoomStateData(dirty, RoomStateEnum.getDescription(dirty), roomMapper.countByHotelIdAndRoomStatus(currentHotelId, dirty),3));
            //维修房
            stateList.add(getCurrentRoomStateData(oo, RoomStateEnum.getDescription(oo), roomMapper.countByHotelIdAndRoomStatus(currentHotelId, oo),3));

            RoomTypeCache rmtCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
//
            List<RoomType> roomTypeList = rmtCache.getDataList(currentHotelId); //roomTypeMapper.findAllByHotelId(currentHotelId);
            for (RoomType roomType : roomTypeList) {
                stateList.add(getCurrentRoomStateData(roomType.getRoomType(), roomType.getDescription(),roomType.getRoomNumber(),2));
            }

            stateList.add(getCurrentRoomStateData("1", "占用",roomMapper.countByHotelIdAndLocc(currentHotelId, StatusTypeUtils.RoomLocc.OCCUPY),1));
        }
        //统计房态数据
        //干净房
//        stateList.add(getCurrentRoomStateData(clean, RoomStateEnum.getDescription(clean), roomMapper.countByHotelIdAndRoomStatus(currentHotelId, clean),3));
//        //脏房
//        stateList.add(getCurrentRoomStateData(dirty, RoomStateEnum.getDescription(dirty), roomMapper.countByHotelIdAndRoomStatus(currentHotelId, dirty),3));
//        //维修房
//        stateList.add(getCurrentRoomStateData(oo, RoomStateEnum.getDescription(oo), roomMapper.countByHotelIdAndRoomStatus(currentHotelId, oo),3));
        //统计所有房型数据
//        RoomTypeCache rmtCache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
//
//        List<RoomType> roomTypeList = rmtCache.getDataList(currentHotelId); //roomTypeMapper.findAllByHotelId(currentHotelId);
//        for (RoomType roomType : roomTypeList) {
//            stateList.add(getCurrentRoomStateData(roomType.getRoomType(), roomType.getDescription(),roomType.getRoomNumber(),2));
//        }
        //统计占用状态
//        stateList.add(getCurrentRoomStateData("1", "占用",roomMapper.countByHotelIdAndLocc(currentHotelId, StatusTypeUtils.RoomLocc.OCCUPY),1));
//        stateList.add(getCurrentRoomStateData("1", "占用",roomMapper.findAllByHotelIdAndLoccAndRoomType(currentHotelId, StatusTypeUtils.RoomLocc.OCCUPY),1));

        statusRes.setCurrentRoomStatusDataList(stateList);
        return statusRes;
    }

    /**
     * 实时房态列表
     *
     * @param queryRoomStateReq 查询房间状态的请求对象，包含房间状态、房间号、房型等过滤条件及分页信息。
     * @return 返回房间状态列表的响应对象，包含查询结果及分页信息。
     */
    @Override
    public RoomListRes listRoomStates(QueryRoomStateReq queryRoomStateReq) {
        //房态
        String roomStatus = queryRoomStateReq.getRoomStatus();
        //房号
        String roomNo = queryRoomStateReq.getRoomNo();
        //房型
        String roomType = queryRoomStateReq.getRoomType();
        //占用标识
        int locc = queryRoomStateReq.getLocc();
        // 初始化查询条件列表，用于指定查询规范
        List<SearchCriteria> allList = new ArrayList<>();
        // 只有当locc == 1时，才添加占用标识查询条件
        if(locc == StatusTypeUtils.RoomLocc.OCCUPY){
            allList.add(new SearchCriteria("locc", locc, Operator.EQUAL));
        }
        // 添加房间类型查询条件
        if (StringUtil.isNotEmpty(roomType)) {
            allList.add(new SearchCriteria("roomType", roomType, Operator.EQUAL));
        }
        // 添加房间号匹配条件
        if (StringUtil.isNotEmpty(roomNo)) {
            allList.add(new SearchCriteria("roomNo", roomNo, Operator.LIKE));
        }

        // 遍历房间状态列表，添加房间状态匹配条件
        //allList.add(new SearchCriteria("roomStatus", roomStatus, Operator.EQUAL));

        if (StrUtil.isNotBlank(roomStatus)) {
            allList.add(new SearchCriteria("roomStatus", Arrays.asList(roomStatus.split(",")), Operator.IN));
        }

        if (StrUtil.isNotBlank(queryRoomStateReq.getBuildingNo())) {//如果有楼号，则只显示楼号内的房间
            RoomTypeCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
            List<RoomType> roomTypes = cache.getDataList(GlobalContext.getCurrentHotelId());
            List<String> roomTypeNames = roomTypes.stream().filter(r -> r.getBuildingNo().equals(queryRoomStateReq.getBuildingNo())).map(RoomType::getRoomType).collect(Collectors.toList());
            allList.add(new SearchCriteria("roomType", roomTypeNames, Operator.IN));
        }


        // 添加查询条件：酒店ID等于当前酒店ID
        allList.add(new SearchCriteria("hotelId", GlobalContext.getCurrentHotelId(), Operator.EQUAL));
        // 根据查询条件列表构建查询规范
        Specification<Room> specification = DynamicSpecificationBuilder.getQuerySpecification(Room.class, allList);

        // 根据查询规范和分页信息，查询房间信息
        Page<Room> rooms = roomMapper.findAll(specification, JpaUtil.getPageRequest(queryRoomStateReq.getPages()));
        // 根据查询结果和分页信息，构建并返回房间列表的响应对象
        RoomListRes listRes = PageResUtil.fillPagesDate(rooms, RoomListRes.class);

        FloorCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.FLOOR);
        List<Floor> floors = cache.getDataList(GlobalContext.getCurrentHotelId());
        Map<String, Integer> floorMap = floors.stream().collect(Collectors.toMap(Floor::getCode, Floor::getSeq));


        for (RoomRes roomRes : listRes.getRecords()) {
            String floorCode = roomRes.getFloorcode();
            Integer seq = floorMap.getOrDefault(floorCode, null);
            roomRes.setFloorSeq(seq);
        }
        List<RoomRes> sortedRecords = listRes.getRecords().stream()
                .sorted(Comparator.comparingInt((RoomRes roomRes) -> {
                    Integer floorSeq = roomRes.getFloorSeq();
                    if (floorSeq != null) {
                        return floorSeq;
                    }
                    // floorSeq 为空，返回最大值，排在最后
                    floorSeq = Integer.MAX_VALUE;
                    roomRes.setFloorSeq(floorSeq);
                    return floorSeq;
                }).thenComparing(RoomRes::getRoomNo))
                .collect(Collectors.toList());

        listRes.setRecords(sortedRecords);
        for (RoomRes record : listRes.getRecords()) {
            record.setDescription(CustomData.getDesc(GlobalContext.getCurrentHotelId(), record.getRoomType(), SystemUtil.CustomDataKey.roomtype));
        }

        // 使用合并函数处理重复键，这里选择保留第一个出现的 RoomRes 对象
        Map<String,RoomRes> roomResMap = listRes.getRecords().stream().collect(Collectors.toMap(
                RoomRes::getRoomNo,
                room -> room,
                (existing, replacement) -> existing
        ));
        // 获取占用的房间号
        List<String> occRoom = listRes.getRecords().stream().filter(r -> r.getLocc() == 1).map(RoomRes::getRoomNo).collect(Collectors.toList());
        // 根据房间号获取相关预订
        List<Reservation> reservations = daoLocal.getObjectList("from Reservation where hotelId = ?1 and roomNumber in ?2",GlobalContext.getCurrentHotelId(), occRoom);
        // 处理预订数据
        for (Reservation reservation : reservations) {
            RoomRes roomRes = roomResMap.get(reservation.getRoomNumber());
            StringBuilder guestInfo = new StringBuilder();
            if (ObjectUtil.isNotNull(roomRes)) {
                guestInfo.append(reservation.getGuestName());
                List<Accompany> accompanyList = JSON.parseArray(reservation.getAccompany(), Accompany.class);
                if (CollectionUtil.isNotEmpty(accompanyList)) {
                    for (Accompany accompany : accompanyList) {
                        if (StringUtil.isNotBlank(guestInfo.toString())) {
                            guestInfo.append("/").append(accompany.getGuestName());
                        }
                    }
                }
            }
            roomRes.setGuestInfo(guestInfo.toString());
        }
        return listRes;
    }

    @Override
    public RoomRes load(Common_Load_Req req) {
        RoomRes res = new RoomRes();
        Room room = roomMapper.findById(req.getId()).orElse(null);
        if (room != null) {
            BeanUtil.copyProperties(room, res);
            BeanUtil.copyProperties(room, res, CopyOptions.create().ignoreNullValue());
        } else {
            throw new BizException("房间不存在");
        }
        return res;
    }

    @Override
    public RoomListRes listOperationRoomStates(QueryOperationRoomStateReq queryOperationRoomStateReq) {
        List<String> roomStatusList = queryOperationRoomStateReq.getRoomStatus();
        //酒店id
        String currentHotelId = GlobalContext.getCurrentHotelId();
        String buildingNo = GlobalContext.getCurrentBuildingNo();
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("roomStatus", roomStatusList, Operator.IN));
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", currentHotelId, Operator.EQUAL));


        if (StrUtil.isNotBlank(buildingNo)) {
            // 使用daoLocal查询该楼栋下的所有房型
            List<String> roomTypes = daoLocal.getObjectList(
                    "select roomType from RoomType where hotelId = ?1 and buildingNo = ?2",
                    currentHotelId,
                    buildingNo
            );
            if (CollectionUtil.isNotEmpty(roomTypes)) {
                // 2. 添加房型过滤条件：房间类型 IN 提取的房型代码列表
                conditionList.add(new SearchCriteria("roomType", roomTypes, Operator.IN));
            }
        }

        Specification<Room> specification = DynamicSpecificationBuilder.getQuerySpecification(Room.class, conditionList);
        Page<Room> rooms = roomMapper.findAll(specification, JpaUtil.getPageRequest(queryOperationRoomStateReq.getPages()));
        RoomListRes list = PageResUtil.fillPagesDate(rooms, RoomListRes.class);

        for (RoomRes record : list.getRecords()) {
            record.setDescription(CustomData.getDesc(currentHotelId, record.getRoomType(), SystemUtil.CustomDataKey.roomtype));
        }


        return list;
    }

    /**
     * 远期可用房态列表
     */
    @Override
    public FutureRoomStateRes listFutureRoomStates(QueryFutureRoomStateReq req) {
        String hotelId = GlobalContext.getCurrentHotelId();

        Set<String> filter = CollectionUtil.isNotEmpty(req.getRoomTypeList()) ? CollectionUtil.newHashSet(req.getRoomTypeList()) : Sets.newHashSet();

        List<RoomType> roomTypeList = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE).getDataList(hotelId);

        if (StrUtil.isNotBlank(req.getBuildingNo())) {
            roomTypeList = roomTypeList.stream().filter(r -> r.getBuildingNo().equals(req.getBuildingNo())).collect(Collectors.toList());
        }

        //查询该日期段对应的所有可卖房量数据
        List<RroomsAvlPo> dbData = roomQuantityMapper.queryRroomsSchedule(hotelId, req.getStartdate(), req.getEnddate());
        //
        Table<String, String, RroomsAvlPo> dbTable = HashBasedTable.create();//创建一个二维表 对应前端数据
        TreeMap<String, DailyRoomTypeData> sumMap = Maps.newTreeMap();

        for (RroomsAvlPo avlPo : dbData) {
            dbTable.put(avlPo.getRoomType(), CalculateDate.dateToString(avlPo.getDatum()), avlPo);
        }

        int length = CalculateDate.compareDates(req.getEnddate(), req.getStartdate()).intValue();
        List<String> dates = Lists.newArrayList();
        for (int i = 0; i < length; i++) {
            Date d = CalculateDate.reckonDay(req.getStartdate(), 5, i);
            String strd = CalculateDate.dateToString(d);
            dates.add(strd);
        }

        FutureRoomStateRes res = new FutureRoomStateRes();
        for (RoomType roomType : roomTypeList) {
            if (filter.size() > 0 && !filter.contains(roomType.getRoomType())) {
                continue;
            }
            Map<String, RroomsAvlPo> roomTypeAvlMap = dbTable.row(roomType.getRoomType());
            FutureRoomStateRes.RoomTypeFutureData rmtRowData = new FutureRoomStateRes.RoomTypeFutureData();
            rmtRowData.setTotal(roomType.getRoomNumber());
            rmtRowData.setRoomType(roomType.getRoomType());
            rmtRowData.setDesc(roomType.getDescription());

            for (String strDate : dates) {
                DailyRoomTypeData data = new DailyRoomTypeData();
                data.setDate(strDate);
                RroomsAvlPo po = roomTypeAvlMap.get(strDate);
                if (po != null) {
                    data.setPickup(po.getPickup());
                    data.setAvl(po.getAvl());
                    //维修总数
                    data.setOoo(po.getOoo());
                }
                rmtRowData.getDailyList().add(data);
                if (!sumMap.containsKey(strDate)) {
                    sumMap.put(strDate, new DailyRoomTypeData());
                }
                DailyRoomTypeData sumData = sumMap.get(strDate);
                sumData.setPickup(sumData.getPickup() + data.getPickup());
                sumData.setAvl(sumData.getAvl() + data.getAvl());
                //维修总数
                sumData.setOoo(sumData.getOoo() + data.getOoo());
            }
            res.getRmtData().add(rmtRowData);
        }
        res.calcSumData(sumMap);

        return res;
    }

    /**
     * 查询房间列表
     */
    @Override
    public RoomListRes listRooms(QueryRoomReq queryRoomReq) {
        String hotelId = GlobalContext.getCurrentHotelId();
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("roomNo", queryRoomReq.getRoomNo(), Operator.LIKE));
        conditionList.add(new SearchCriteria("roomType", queryRoomReq.getRoomType(), Operator.EQUAL));
        conditionList.add(new SearchCriteria("roomStatus", queryRoomReq.getRoomStatus(), Operator.EQUAL));
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", hotelId, Operator.EQUAL));
        Specification<Room> specification = DynamicSpecificationBuilder.getQuerySpecification(Room.class, conditionList);
        Page<Room> rooms = roomMapper.findAll(specification, JpaUtil.getPageRequest(queryRoomReq.getPages()));
        RoomListRes result = PageResUtil.fillPagesDate(rooms, RoomListRes.class);
        if (CollectionUtil.isNotEmpty(result.getRecords())) {
            for (RoomRes node: result.getRecords()) {
                //床型房间特性描述
                if (StringUtils.isNotBlank(node.getCharacteristic())) {
                    String[] descs = node.getCharacteristic().split(",");
                    String rowdesc = "";
                    for (String desc : descs) {
                        String descName = CustomData.getDesc(hotelId, desc, SystemUtil.CustomDataKey.characteristic);
                        rowdesc += rowdesc.isEmpty() ? descName : "," + descName;
                    }
                    node.setCharacteristic(rowdesc);
                }

                node.setDescription(CustomData.getDesc(hotelId, node.getRoomType(), SystemUtil.CustomDataKey.roomtype));

            }
        }
        return result;
    }

    /**
     * 查询可预定的房间列表
     */
    @Override
    public RoomListRes listRegisterRooms(QueryRegisterRoomReq queryRegisterRoomReq) {
        //如果前端不传，默认使用当天的日期
        String today = DateUtil.format(new Date(), "yyyy-MM-dd");
        //入住日期
        String arrivalDateStr = queryRegisterRoomReq.getArrivalDate();
        DateTime arrivalDate = DateUtil.parse(StrUtil.isNotEmpty(arrivalDateStr) ? arrivalDateStr : today, "yyyy-MM-dd");
        //特性
        String characteristic = queryRegisterRoomReq.getCharacteristic();
        //房态
        String roomStatus = queryRegisterRoomReq.getRoomStatus();
        //查询起始房间
        String roomNoStart = queryRegisterRoomReq.getRoomNoStart();
        //查询结束房间
        String roomNoEnd = queryRegisterRoomReq.getRoomNoEnd();
        //房型
        String roomType = queryRegisterRoomReq.getRoomType();
        //分页数据
        PageReq.PageData pages = queryRegisterRoomReq.getPages();

        String hotelId = GlobalContext.getCurrentHotelId();

        RoomListRes roomListRes = null;
        //查询的房间范围
        ArrayList<String> roomList = getRoomRange(roomNoStart, roomNoEnd);
        //如果入住日期是当天的则查询当天的房间表即可
        DateTime todayDate = DateUtil.parse(today, "yyyy-MM-dd");
        if (DateUtil.compare(todayDate, arrivalDate) == 0) {
            //房态
            List<String> roomStatusList;
            //如果roomStatus为空，则默认查询非占用的干净房
            if (StrUtil.isNotEmpty(roomStatus)) {
                roomStatusList = Arrays.asList(roomStatus.split(","));
            } else {
                roomStatusList = Lists.newArrayList();
                roomStatusList.add(StatusTypeUtils.RoomStatus.CLEAN);
            }
            roomListRes = getRegisterRoomRes(roomType, characteristic, roomList, roomStatusList, "0", Operator.BETWEEN, pages);
        }else {
            //如果预定不是当天的房间，则需要通过查询预定表和房间表来筛选符合条件的房间
            //记录需要被过滤的房间号
            ArrayList<String> occupyRoomList = getOccupyRooms(arrivalDate, queryRegisterRoomReq.getDepartureDate());
            //所有符合的房间
            roomListRes = getRegisterRoomRes(roomType, characteristic, occupyRoomList, null, "", Operator.NOT_IN, pages); //根据占用的预订表排查一遍
            List<RoomRes> records = roomListRes.getRecords();
            //此处筛选出来的未来房间，都是空闲的干净房
            //for (RoomRes roomRes : records) {
            //    roomRes.setRoomStatus(StatusTypeUtils.RoomStatus.CLEAN);
            //    roomRes.setLocc(StatusTypeUtils.RoomLocc.UN_OCCUPY);
            //}
            //如果房间范围是空的，则直接返回
            //if (ObjectUtil.isEmpty(roomList)) {
            //    return roomListRes;
            //}

            if (!roomList.isEmpty()) {
                //根据房间范围筛选符合条件的房间列表，后续还要过滤
                List<Room> roomsRecords = getRegisterRoomList(roomType, characteristic, roomList, null, "", Operator.BETWEEN);
                List<RoomRes> roomResult = Lists.newArrayList();
                for (RoomRes roomRes : records) {
                    String roomNo = roomRes.getRoomNo();
                    for (Room res : roomsRecords) {
                        //根据房间范围过滤所有不符合的房间
                        if (roomNo.equals(res.getRoomNo())) {
                            roomResult.add(roomRes);
                            break;
                        }
                    }
                }
                roomListRes = paginateResults(roomResult, queryRegisterRoomReq.getPages());
            }

        }

        if (CollectionUtil.isNotEmpty(roomListRes.getRecords())) {
            for (RoomRes node : roomListRes.getRecords()) {
                //床型房间特性描述
                if (StringUtils.isNotBlank(node.getCharacteristic())) {
                    String[] descs = node.getCharacteristic().split(",");
                    String rowdesc = "";
                    for (String desc : descs) {
                        String descName = CustomData.getDesc(hotelId, desc, SystemUtil.CustomDataKey.characteristic);
                        rowdesc += rowdesc.isEmpty() ? descName : "," + descName;
                    }
                    node.setCharacteristic(rowdesc);
                }

            }
        }

        return roomListRes;
    }

    /**
     * 获取所有不符合的房间（后续过滤掉这些房间，剩下就是可以被预定的房间）
     *
     * @param arrivalDate      到店日期
     * @param departureDateStr 离店日期
     * @return 所有预定的房间
     */
    private ArrayList<String> getOccupyRooms(Date arrivalDate, String departureDateStr) {
        //如果入住的日期不是当天的，即未来的房间；则需要通过查询预定表和房间表，对已经存在的预定、在住、应离未离房间，以及维修房进行过滤
        String hotelId = GlobalContext.getCurrentHotelId();
        // 创建条件列表
        List<SearchCriteria> conditionList = new ArrayList<>();
        // 可选房间需要过滤状态：预定，入住，应离未离
        ArrayList<Integer> reservationStatusList = Lists.newArrayList();
        reservationStatusList.add(StatusTypeUtils.RsStatus.EXPECTED);
        reservationStatusList.add(StatusTypeUtils.RsStatus.CHECKIN);
        //reservationStatusList.add(StatusTypeUtils.RsStatus.NOT_DEPARTED);
        conditionList.add(new SearchCriteria("reservationStatus", reservationStatusList, Operator.IN));
        conditionList.add(new SearchCriteria("hotelId", hotelId, Operator.EQUAL));
        // 查询预订信息
        Specification<Reservation> querySpecification = DynamicSpecificationBuilder.getQuerySpecification(Reservation.class, conditionList);
        List<Reservation> reservationList = reservationMapper.findAll(querySpecification);
        //如果离店日期为空，暂且默认第二天离店
        DateTime departureDate = StrUtil.isEmpty(departureDateStr) ? DateUtil.offsetDay(arrivalDate, 1) : DateUtil.parse(departureDateStr, "yyyy-MM-dd");
        ArrayList<String> roomList = Lists.newArrayList();
        //记录所有被占用的房子
        for (Reservation reservation : reservationList) {
            String roomNumber = reservation.getRoomNumber();
            if (StrUtil.isEmpty(roomNumber)) {
                continue;
            }
            //前大于后（前面时间迟于后者），大于0;相等（前面时间等于后者），等于0；前小于后（前面时间早于后者），小于0
            Date reArrivalDate = reservation.getArrivalDate();
            int arriCompare = DateUtil.compare(arrivalDate, reArrivalDate);
            Date reDepartureDate = reservation.getDepartureDate();
            //1) 如果入住日期迟于预定的入住日期，且退房日期迟于预定的退房日期，证明该房间被预定，该房间则不能被再次分房
            int depCompare = DateUtil.compare(departureDate, reDepartureDate);
            if (arriCompare > 0 && depCompare < 0) {
                roomList.add(roomNumber);
                continue;
            }
            //入住日期早于预定的入住日期，离店日期晚于预定的入住日期，该房间被预定，同样也不能被再次分房
            int compare = DateUtil.compare(departureDate, reArrivalDate);
            //2) 如果入住日期在预定的入住日期之前，但是离店日期在预定日期的入住日期之后，该房间不能被再次分房
            if (arriCompare < 0 && compare > 0) {
                roomList.add(roomNumber);
            }
        }
        //记录维修房，方便后面过滤
        List<Room> ooRooms = roomMapper.findAllByHotelIdAndRoomStatus(hotelId, StatusTypeUtils.RoomStatus.OO);
        for (Room room : ooRooms) {
            String endTime = room.getEndTime();
            if (StrUtil.isEmpty(endTime)) {
                continue;
            }
            DateTime endDate = DateUtil.parse(endTime, "yyyy-MM-dd");
            DateTime startDate = DateUtil.parse(room.getStartTime(), "yyyy-MM-dd");
            //如果入住日期迟于该房间的维修开始时间，且早于该房间的维修结束时间，该房间不能被再次分房
            int ooStartCompare = DateUtil.compare(arrivalDate, startDate);
            int ooEndCompare = DateUtil.compare(arrivalDate, endDate);
            int ooDepCompare = DateUtil.compare(departureDate, endDate);
            if (ooStartCompare > 0 && ooEndCompare < 0) {
                roomList.add(room.getRoomNo());
                continue;
            }
            //如果入住日期早于该房间的维修开始时间，且退房时间在维修期间，该房间不能被再次分房
            if (ooStartCompare < 0 && ooEndCompare > 0 && ooDepCompare < 0) {
                roomList.add(room.getRoomNo());
            }
        }
        return roomList;
    }
    @Override
    public LiveInRoomListRes listLiveInRooms(QueryLiveInRoomReq queryLiveInRoomReq) {
        String currentHotelId = GlobalContext.getCurrentHotelId();
        List<SearchCriteria> conditionList = new ArrayList<>();
        //房间状态
        conditionList.add(new SearchCriteria("reservationStatus", queryLiveInRoomReq.getReservationStatus(), Operator.EQUAL));
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", currentHotelId, Operator.EQUAL));
        Specification<Reservation> specification = DynamicSpecificationBuilder.getQuerySpecification(Reservation.class, conditionList);
        PageReq.PageData pages = queryLiveInRoomReq.getPages();
        Page<Reservation> rooms = reservationMapper.findAll(specification, JpaUtil.getPageRequest(pages));
        //房型
        List<RoomType> roomTypes = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE).getDataList(currentHotelId);
        //响应在住房间列表
        LiveInRoomListRes liveInRoomListRes = new LiveInRoomListRes();
        ArrayList<LiveInRoomRes> liveInRoomResList = Lists.newArrayList();
        for(Reservation reservation : rooms.getContent()){
            LiveInRoomRes liveInRoomRes = new LiveInRoomRes();
            BeanUtil.copyProperties(reservation,liveInRoomRes);
            for(RoomType roomType : roomTypes){
                if(roomType.getRoomType().equals(reservation.getRoomType())){
                    //将房型代码转换为对应的房型描述
                    liveInRoomRes.setDescription(roomType.getDescription());
                    break;
                }
            }
            liveInRoomResList.add(liveInRoomRes);
        }
        liveInRoomListRes.setRecords(liveInRoomResList);
        liveInRoomListRes.setCurrentpage(pages.getCurrentpage());
        liveInRoomListRes.setPagesize(pages.getPagesize());
        liveInRoomListRes.setTotalpage(rooms.getTotalPages());
        liveInRoomListRes.setTotal(Integer.parseInt(rooms.getTotalElements()+""));
        return liveInRoomListRes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRoom(Long id) {
        //TODO  判断是否有预订在使用该房间
        Optional<Room> roomOptional = roomMapper.findById(id);
        if (!roomOptional.isPresent()) {
            throw new BizException(StrUtil.format("找不到对应的id为{}的房间数据", id));
        }
        Room room = roomOptional.get();
        //如果该房间为占用，则表示该房间有预定在使用，无法删除
        if(room.getLocc() == StatusTypeUtils.RoomLocc.OCCUPY){
            throw new BizException(StrUtil.format("房间{}正在被占用，无法删除！", room.getRoomNo()));
        }
        String roomType = room.getRoomType();
        String currentHotelId = GlobalContext.getCurrentHotelId();
        daoLocal.batchOption("update Rrooms set total=total-1 where hotelId=?1 and roomType=?2", currentHotelId, roomType);
        daoLocal.batchOption("update RoomType set roomNumber=roomNumber-1 where hotelId=?1 and roomType=?2", currentHotelId, roomType);

        //删除对应的房间
        roomMapper.deleteById(id);
        //更新房型缓存，避免远期房态数量不同步
        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOMTYPE, currentHotelId);
        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOM, currentHotelId);
    }

    /**
     * 获取非预离的在住房
     *
     * @param roomResList 房间列表
     * @return 非预离的在住房列表
     */
    private ArrayList<RoomRes> getNotLeaveRooms(ArrayList<RoomRes> roomResList) {
        ArrayList<RoomRes> loccRoomsList = Lists.newArrayList();
        for (RoomRes roomRes : roomResList) {
            //1表示为占用房
            int locc = roomRes.getLocc();
            //true表示该房间为预离房，不能被过滤
            boolean toLeaveFlg = roomRes.isToLeaveFlg();
            //过滤房间为占用且isToLeaveFlg为false的非预离的在住房
            if (locc == StatusTypeUtils.RoomLocc.OCCUPY && !toLeaveFlg) {
                loccRoomsList.add(roomRes);
            }
        }
        return loccRoomsList;
    }

    /**
     * 判断在查询日期内该房间是否为维修房
     *
     * @param futureDate 查询的日期
     * @param endTime    维修结束时间
     * @return 在查询日期内该房间是否为维修房
     */
    private boolean isOoo(String futureDate, String endTime) {
        //查询的日期在维修结束日期之前，则该房间为维修房
        return DateUtil.compare(DateUtil.parse(futureDate, "yyyy-MM-dd"), DateUtil.parse(endTime, "yyyy-MM-dd")) < 0;
    }

    /**
     * 根据房间列表和预离房间列表，生成房间资源列表。
     * 此函数通过遍历房间列表，复制每个房间的信息到房间资源对象中，并根据退房房间列表标记需要退房的房间。
     *
     * @param roomList     房间列表，包含所有房间的信息。
     * @param toLeaveRooms 预离房间列表，包含需要标记为预离的房间号。
     * @return 房间资源列表，包含根据原房间列表生成的房间资源对象，其中预离房间被标记。
     */
    private ArrayList<RoomRes> getRoomResList(List<Room> roomList, ArrayList<String> toLeaveRooms) {
        // 将toLeaveRooms转换为HashSet以优化性能
        Set<String> toLeaveRoomsSet = new HashSet<>(toLeaveRooms);
        ArrayList<RoomRes> roomResList = Lists.newArrayList();
        for (Room room : roomList) {
            RoomRes roomRes = new RoomRes();
            BeanUtil.copyProperties(room, roomRes);
            // 使用HashSet优化预离房的检查
            if (toLeaveRoomsSet.contains(room.getRoomNo())) {
                roomRes.setToLeaveFlg(true);
            }
            roomResList.add(roomRes);
        }
        return roomResList;
    }

    /**
     * 获取响应的预离房间列表
     * @param roomList 房间列表
     * @param toLeaveRooms 预离房间号列表
     * @return 响应的预离房间列表
     */
    private ArrayList<RoomRes> getAllToLeaveRooms(List<Room> roomList, ArrayList<String> toLeaveRooms) {
        // 将toLeaveRooms转换为HashSet以优化性能
        Set<String> toLeaveRoomsSet = new HashSet<>(toLeaveRooms);
        ArrayList<RoomRes> roomResList = Lists.newArrayList();
        for (Room room : roomList) {
            // 使用HashSet优化预离房的检查
            if (toLeaveRoomsSet.contains(room.getRoomNo())) {
                RoomRes roomRes = new RoomRes();
                BeanUtil.copyProperties(room, roomRes);
                roomRes.setToLeaveFlg(true);
                roomResList.add(roomRes);
            }
        }
        return roomResList;
    }

    /**
     * 从预定中获取预离的房间号列表
     *
     * @return ArrayList<String> 包含即将退房的房间号码的列表。
     */
    private ArrayList<String> getToLeaveRoomNos() {
        // 查询所有预订信息
        List<Reservation> reservations = findReservedRooms("","","");
        //先记录所有预离的房间，方便后续作处理
        ArrayList<String> toLeaveRooms = Lists.newArrayList();
        // 获取当前日期
        Date currentDate = new Date();
        // 遍历所有预订信息
        reservations.forEach(reservation -> {
            // 获取预订的退房日期
            Date departureDate = reservation.getDepartureDate();
            String roomNumber = reservation.getRoomNumber();
            // 如果退房日期不为空
            if (departureDate != null && StrUtil.isNotEmpty(roomNumber)) {
                // 判断退房日期与当前日期的差距是否小于一天
                // 离店时间和当天的时间比较，如果小于1天，则被定义为预离房,true表示只比较天，不比较时分钟
                if (DateUtil.betweenDay(departureDate, currentDate, true) < 1) {
                    // 如果差距小于一天，将房间号码加入到即将退房的房间列表中
                    toLeaveRooms.add(roomNumber);
                }
            }
        });
        // 返回即将退房的房间列表
        return toLeaveRooms;
    }

    /**
     * 根据酒店ID和描述获取或创建房型。
     * <p>
     * 本方法首先尝试根据当前酒店ID和房型描述查询已存在的房型。如果房型存在，则直接返回该房型对象。
     * 如果房型不存在，则抛出一个业务异常，提示用户该房型不存在，并建议在房型管理处添加该房型。
     *
     * @param hotelId      当前酒店的ID，用于查询特定酒店下的房型。
     * @param postRoomType 房型代码
     * @return 查询到或新创建的房型对象。
     * @throws BizException 如果房型不存在，则抛出此业务异常。
     */
    private RoomType findRoomType(String hotelId, String postRoomType) {
        // 根据酒店ID和描述查询房型
        RoomType roomType = roomTypeMapper.findByHotelIdAndRoomType(hotelId, postRoomType);
        // 检查查询结果，如果房型不存在，则抛出业务异常
        if (ObjectUtils.isEmpty(roomType)) {
            throw new BizException("该房型不存在，请在房型管理处添加该房型");
        }
        // 返回查询到的房型
        return roomType;
    }

    /**
     * 检查指定酒店是否存在指定房间。
     * <p>
     * 通过调用roomMapper的findRoomByHotelIdAndRoomNo方法，传入当前酒店ID和房间号，
     * 来查询数据库中是否存在对应的房间记录。如果存在返回true，否则返回false。
     *
     * @param currentHotelId 当前酒店的ID，用于指定查询的酒店范围。
     * @param roomNo         指定的房间号，用于精确查询房间信息。
     * @return 如果房间存在则返回true，否则返回false。
     */
    private boolean isRoomExists(String currentHotelId, String roomNo) {
        return ObjectUtils.isNotEmpty(roomMapper.findRoomByHotelIdAndRoomNo(currentHotelId, roomNo));
    }


    /**
     * 在原来房型的基础上新增数据
     * 此方法用于在数据库中更新特定房间类型的房间数量，基于当前的房间数量和传入的新房间数量进行计算。
     *
     * @param roomType     房型。
     * @param newRoomCount 新的房间数量，用于计算更新后的房间数量。
     */
    private void updateRoomTypeRoomNumber(RoomType roomType, int newRoomCount) {
        // 计算更新后的房间数量
        int updatedRoomNumber = roomType.getRoomNumber() + newRoomCount;
        // 设置更新后的房间数量
        roomType.setRoomNumber(updatedRoomNumber);
        // 保存更新后的房间类型到数据库
        roomTypeMapper.save(roomType);

        GlobalCache.getInstance().refreshAndNotify(GlobalDataType.ROOMTYPE, GlobalContext.getCurrentHotelId());
    }


    /**
     * 根据酒店ID和房间类型更新可卖房量
     * 如果给定的酒店ID和房间类型已经在数据库中存在，则更新可卖房量；
     * 如果不存在，则创建一个新的可卖房量记录。
     *
     * @param currentHotelId 当前酒店的ID
     * @param roomType       房型
     * @param newRoomCount   新的房间数量
     */
    private void updateRoomQuantity(String currentHotelId, String roomType, int newRoomCount) {
        try {
            // 根据酒店ID和房间类型查询现有的房间数量记录
            //Rrooms rrooms = roomQuantityMapper.findByHotelIdAndRoomType(currentHotelId, roomType);
            //
            //if (ObjectUtils.isNotEmpty(rrooms)) {
            //    // 如果记录存在，则更新房间数量和更新时间
            //    rrooms.setDatum(new Date());
            //    rrooms.setTotal(rrooms.getTotal() + newRoomCount);
            //} else {
            //    // 如果记录不存在，则创建新的房间数量记录并初始化相关字段
            //    rrooms = new Rrooms();
            //    rrooms.setHotelId(currentHotelId);
            //    rrooms.setTotal(newRoomCount);
            //    rrooms.setPickup(0);
            //    rrooms.setRoomType(roomType);
            //    rrooms.setDatum(new Date());
            //    rrooms.setOoo(0);
            //}
            //// 保存或更新房间数量记录
            //roomQuantityMapper.save(rrooms);

            daoLocal.batchOption("update Rrooms set total=total+?1 where hotelId=?2 and roomType=?3", newRoomCount, currentHotelId, roomType);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException(StrUtil.format("新增该房型{}的房间时，更新对应房型的可卖房数量失败，检查该可卖房是否存在多条该房型记录!", roomType));
        }
    }


    /**
     * 房间资源列表。
     *
     * @param allRooms    所有房间的列表。
     * @return 房间资源列表。
     */
    private List<RoomRes> getPageRoomRes(List<Room> allRooms) {
        return allRooms.stream().map(room -> {
            RoomRes roomRes = new RoomRes();
            BeanUtil.copyProperties(room, roomRes);
            return roomRes;
        }).collect(Collectors.toList());
    }


    /**
     * 查找已预订的房间。
     * 该方法通过构建搜索条件来查询当前酒店的所有已预订房间信息。
     *
     * @return 返回一个包含所有已预订房间的列表。
     */
    private List<Reservation> findReservedRooms(String roomType,String roomNumber, String reservationStatus) {
        // 初始化一个搜索条件列表，用于后续构建查询规范
        List<SearchCriteria> reservationConditionList = new ArrayList<>();
        if (StrUtil.isNotEmpty(roomType)) {
            // 添加搜索条件：仅查询当前酒店的预订信息
            reservationConditionList.add(new SearchCriteria("roomType", roomType, Operator.EQUAL));
        }
        // 添加搜索条件：仅查询当前酒店的预订信息
        reservationConditionList.add(new SearchCriteria("hotelId", GlobalContext.getCurrentHotelId(), Operator.EQUAL));
        // 预订状态
        if(StrUtil.isNotEmpty(reservationStatus)){
            reservationConditionList.add(new SearchCriteria("reservationStatus", Arrays.asList(reservationStatus.split(",")), Operator.IN));
        }
        // 房间号码
        if(StrUtil.isNotEmpty(roomNumber)){
            reservationConditionList.add(new SearchCriteria("roomNumber", roomNumber, Operator.EQUAL));
        }
        // 查询预订信息
        // 根据搜索条件列表构建查询规范
        Specification<Reservation> reservationSpecification = DynamicSpecificationBuilder.getQuerySpecification(Reservation.class, reservationConditionList);
        // 根据查询规范查询所有符合条件的预订信息
        return reservationMapper.findAll(reservationSpecification);
    }

    /**
     * 实时房态列表数据
     *
     * @param code        code
     * @param description description
     * @param total       总数
     * @param flg         占用房为1，房型为2、房态为3
     * @return 实时房态列表数据
     */
    private CurrentRoomStateData getCurrentRoomStateData(String code, String description, int total,int flg) {
        CurrentRoomStateData currentRoomStateData = new CurrentRoomStateData();
        currentRoomStateData.setCode(code);
        currentRoomStateData.setDescription(description);
        currentRoomStateData.setTotal(total);
        currentRoomStateData.setFlg(flg);
        return currentRoomStateData;
    }
    /**
     * 处理分页数据并返回
     *
     * @param roomResList 符合条件的数据
     * @param pages       分页
     * @return 返回前端分页数据
     */
    private RoomListRes paginateResults(List<RoomRes> roomResList, PageReq.PageData pages) {
        //查询当前页数
        int currentPage = pages.getCurrentpage();
        //查询的每页大小
        int pageSize = pages.getPagesize();
        //符合条件的总数
        int total = roomResList.size();
        //根据前端请求的分页数据，截取相应数据返回给前端
        int start = (currentPage - 1) * pageSize;
        int end = Math.min(start + pageSize, total);
        // 计算总页数
        int totalPages = (total + pageSize - 1) / pageSize;
        // 确保当前页码不超过总页数
        currentPage = Math.min(currentPage, totalPages);

        // 构建分页响应对象
        RoomListRes roomListRes = new RoomListRes();
        roomListRes.setTotal(total);
        roomListRes.setPagesize(pageSize);
        roomListRes.setCurrentpage(currentPage);
        roomListRes.setTotalpage(totalPages);
        roomListRes.setRecords(roomResList.subList(start, end));
        return roomListRes;
    }


    /**
     * 根据查询条件来筛选房间
     *
     * @param roomType       房型
     * @param characteristic 特性
     * @param roomNoStart    起始房间
     * @param roomNoEnd      结束房间
     * @return true, 为根据其中一个条件筛选房间，false，则查询全部房间
     */
    private boolean isConditionFlg(String roomType, String characteristic, String roomNoStart, String roomNoEnd) {
        return StrUtil.isNotEmpty(roomType) || StrUtil.isNotEmpty(characteristic) || StrUtil.isNotEmpty(roomNoStart) || StrUtil.isNotEmpty(roomNoEnd);
    }

    /**
     * 获取可预定的房间列表
     *
     * @param roomType       房型
     * @param characteristic 特性
     * @param roomStatusList     房态，多个房态以（，）逗号分隔
     * @return 获取除了在住的房间和维修房外，其他房间都可以被预定
     */
    private List<Room> getRegisterRoomList(String roomType, String characteristic, List<String> roomsList,List<String> roomStatusList,String locc, Operator roomOperator) {
        List<SearchCriteria> roomSearchList = getRoomSearchList(roomType, characteristic, roomsList, roomStatusList, locc, roomOperator);
        Specification<Room> specification = DynamicSpecificationBuilder.getQuerySpecification(Room.class, roomSearchList);
        return roomMapper.findAll(specification);
    }

    /**
     * 获取可预定分页响应房间列表
     *
     * @param roomType       房型
     * @param characteristic 特性
     * @param roomStatusList     房态，多个房态以（，）逗号分隔
     * @return 获取除了在住的房间和维修房外，其他房间都可以被预定
     */
    private RoomListRes getRegisterRoomRes(String roomType, String characteristic, List<String> roomsList,List<String> roomStatusList,String locc, Operator roomOperator,PageReq.PageData pages) {
        List<SearchCriteria> roomSearchList = getRoomSearchList(roomType, characteristic, roomsList, roomStatusList, locc, roomOperator);
        Specification<Room> specification = DynamicSpecificationBuilder.getQuerySpecification(Room.class, roomSearchList);
        Page<Room> rooms = roomMapper.findAll(specification, JpaUtil.getPageRequest(pages));
        return PageResUtil.fillPagesDate(rooms, RoomListRes.class);
    }

    /**
     * 查询预定的条件
     *
     * @param roomType       房型
     * @param characteristic 特性
     * @param roomsList      房间集合
     * @param roomStatusList 房态集合
     * @param locc           占用
     * @param roomOperator   操作符
     * @return 查询预定的条件
     */
    private List<SearchCriteria> getRoomSearchList(String roomType, String characteristic, List<String> roomsList, List<String> roomStatusList, String locc, Operator roomOperator) {
        List<SearchCriteria> conditionList = new ArrayList<>();
        //房型
        if (StrUtil.isNotEmpty(roomType)) {
            conditionList.add(new SearchCriteria("roomType", roomType, Operator.EQUAL));
        }
        //特性
        if (StrUtil.isNotEmpty(characteristic)) {
            conditionList.add(new SearchCriteria("characteristic", characteristic, Operator.EQUAL));
        }
        //房间范围
        if (ObjectUtils.isNotEmpty(roomsList)) {
            conditionList.add(new SearchCriteria("roomNo", roomsList, roomOperator));
        }
        if (ObjectUtil.isNotEmpty(roomStatusList)) {
            conditionList.add(new SearchCriteria("roomStatus", roomStatusList, Operator.IN));
        }
        if (StrUtil.isNotEmpty(locc)) {
            conditionList.add(new SearchCriteria("locc", Integer.parseInt(locc), Operator.EQUAL));
        }
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", GlobalContext.getCurrentHotelId(), Operator.EQUAL));
        return conditionList;
    }


    /**
     * 房间筛选范围
     *
     * @param roomNoStart 起始房间
     * @param roomNoEnd   结束房间
     * @return 房间筛选范围
     */
    private ArrayList<String> getRoomRange(String roomNoStart, String roomNoEnd) {
        //房间范围
        ArrayList<String> roomNoList = Lists.newArrayList();
        if(StrUtil.isNotEmpty(roomNoStart)){
            roomNoList.add(roomNoStart);
        }
        if(StrUtil.isNotEmpty(roomNoEnd)){
            roomNoList.add(roomNoEnd);
        }
        return roomNoList;
    }

/*   /**
     * 根据筛选条件获取房间列表
     *
     * @param roomType       房型
     * @param characteristic 特性
     * @param roomNoStart    起始房间
     * @param roomNoEnd      结束房间
     * @return 房间列表
     *
    private List<Room> getRoomList(String roomType, String characteristic, String roomNoStart, String roomNoEnd) {
        List<SearchCriteria> conditionList = new ArrayList<>();
        conditionList.add(new SearchCriteria("roomType", roomType, Operator.EQUAL));
        if (StrUtil.isNotEmpty(characteristic)) {
            //特性
            conditionList.add(new SearchCriteria("characteristic", characteristic, Operator.EQUAL));
        }
        if (StrUtil.isNotEmpty(roomNoStart) && StrUtil.isNotEmpty(roomNoEnd)) {
            //房间范围
            conditionList.add(new SearchCriteria("roomNo", getRoomRange(roomNoStart, roomNoEnd), Operator.BETWEEN));
        }
        // 添加酒店ID条件
        conditionList.add(new SearchCriteria("hotelId", GlobalContext.getCurrentHotelId(), Operator.EQUAL));
        Specification<Room> specification = DynamicSpecificationBuilder.getQuerySpecification(Room.class, conditionList);
        return roomMapper.findAll(specification);
    }*/

  /*/**
     * 可预定的房间除了被预定的房间（注：预离房也可以被预定），以及维修房外，其他房间都可以被预定
     *
     * @param queryRegisterRoomReq queryRegisterRoomReq
     * @return 可预定的房间除了被预定的房间（注：预离房也可以被预定），以及维修房外，其他房间都可以被预定
     *
    private RoomListRes getRoomList(QueryRegisterRoomReq queryRegisterRoomReq) {
        String currentHotelId = GlobalContext.getCurrentHotelId();
        //可预定的房间除了被预定的房间（注：预离房也可以被预定），以及维修房外，其他房间都可以被预定
        String characteristic = queryRegisterRoomReq.getCharacteristic();
        String roomStatus = queryRegisterRoomReq.getRoomStatus();
        String roomNoStart = queryRegisterRoomReq.getRoomNoStart();
        String roomNoEnd = queryRegisterRoomReq.getRoomNoEnd();
        String roomType = queryRegisterRoomReq.getRoomType();
        List<Room> allRooms;
        //1、根据查询条件筛选房间
        if (isConditionFlg(roomType, characteristic, roomNoStart, roomNoEnd)) {
            allRooms = getRoomList(roomType, characteristic, roomNoStart, roomNoEnd);
            //预离房：查询所有预定的房间，遍历每个房间的离店时间和当天的时间比较，如果小于1天，则被定义为预离房
            ArrayList<RoomRes> roomResList = Lists.newArrayList();
            //预离房是否也可以入住
//            if (queryRegisterRoomReq.isToLeaveFlg()) {
//                roomResList = getAllToLeaveRooms(allRooms, getToLeaveRoomNos());
//            }
            //根据房态筛选（备注:因为预离房的房态不是干净房，所以放在这里筛选，如果放在查询里筛选出干净房，则会导致预离房被过滤掉）
            if (StrUtil.isNotEmpty(roomStatus)) {
                //如果筛选为CL，则其他房态移除
                allRooms.removeIf(room -> !room.getRoomStatus().equals(roomStatus));
            }
            //1）维修房：查询所有的房间，如果维修房的结束时间不为空，则表示该房为维修房。
            allRooms.removeIf(room -> StrUtil.isNotEmpty(room.getEndTime()));
            for (Room room : allRooms) {
                RoomRes roomRes = new RoomRes();
                BeanUtil.copyProperties(room, roomRes);
                roomResList.add(roomRes);
            }
            //2）过滤非预离的在住房
            roomResList.removeAll(getNotLeaveRooms(roomResList));
            return paginateResults(roomResList, queryRegisterRoomReq.getPages());
        }
        //2、否则查询全部房间，后续在过滤维修房
        allRooms = roomMapper.findAllByHotelId(currentHotelId);
        //1）维修房：查询所有的房间，如果维修房的结束时间不为空，则表示该房为维修房。
        allRooms.removeIf(room -> StrUtil.isNotEmpty(room.getEndTime()));
        //2) 过滤除非预离的在住房
        ArrayList<RoomRes> roomResList = getRoomResList(allRooms, getToLeaveRoomNos());
        roomResList.removeAll(getNotLeaveRooms(roomResList));
        //3) 处理数据
        return paginateResults(roomResList, queryRegisterRoomReq.getPages());
    }*/

    public RoomListRes listRoomStatesAndRmstat(QueryRoomStateReq queryRoomStateReq) {
        {
            //房态
            String roomStatus = queryRoomStateReq.getRoomStatus();
            //房号
            String roomNo = queryRoomStateReq.getRoomNo();
            //房型
            String roomType = queryRoomStateReq.getRoomType();
            //占用标识
            int locc = queryRoomStateReq.getLocc();
            // 初始化查询条件列表，用于指定查询规范
            List<SearchCriteria> allList = new ArrayList<>();
            // 只有当locc == 1时，才添加占用标识查询条件
            if(locc == StatusTypeUtils.RoomLocc.OCCUPY){
                allList.add(new SearchCriteria("locc", locc, Operator.EQUAL));
            }
            if (StrUtil.isNotBlank(roomType)) {
                // 添加房间类型查询条件
                allList.add(new SearchCriteria("roomType", roomType, Operator.EQUAL));
            }
            if (StrUtil.isNotBlank(roomNo)) {
                // 添加房间号匹配条件
                allList.add(new SearchCriteria("roomNo", roomNo, Operator.EQUAL));
            }
            // 遍历房间状态列表，添加房间状态匹配条件
            //allList.add(new SearchCriteria("roomStatus", roomStatus, Operator.EQUAL));

            if (StrUtil.isNotBlank(roomStatus)) {
                allList.add(new SearchCriteria("roomStatus", Arrays.asList(roomStatus.split(",")), Operator.IN));
            }

            if (StrUtil.isNotBlank(queryRoomStateReq.getBuildingNo())) {//如果有楼号，则只显示楼号内的房间
                RoomTypeCache cache = GlobalCache.getDataStructure().getCache(GlobalDataType.ROOMTYPE);
                List<RoomType> roomTypes = cache.getDataList(GlobalContext.getCurrentHotelId());
                List<String> roomTypeNames = roomTypes.stream().filter(r -> r.getBuildingNo().equals(queryRoomStateReq.getBuildingNo())).map(RoomType::getRoomType).collect(Collectors.toList());
                allList.add(new SearchCriteria("roomType", roomTypeNames, Operator.IN));
            }
            Specification<Room> specification = DynamicSpecificationBuilder.getQuerySpecification(Room.class, allList);;
            if (StrUtil.isEmpty(roomNo) && roomStatus.contains(StatusTypeUtils.RoomStatus.OO)) {
                    specification = (root, query, cb) -> {
                    List<Predicate> predicates = DynamicSpecificationBuilder.getPredicates(Room.class, allList, root, query, cb);

                    // 构建子查询
                    Subquery<String> subquery = query.subquery(String.class);
                    Root<Rmstat> subRoot = subquery.from(Rmstat.class);
                    subquery.select(subRoot.get("roomNo"));
                    // 使用 cb.like 对 status 字段进行模糊查询
                    subquery.where(
                            cb.equal(subRoot.get("hotelId"), GlobalContext.getCurrentHotelId()),
                            cb.equal(subRoot.get("status"),  roomStatus)
                    );

                    // 添加子查询条件
                    predicates.add(root.get("roomNo").in(subquery));

                    return cb.and(predicates.toArray(new Predicate[0]));
                };
            }

            // 根据查询规范和分页信息，查询房间信息
            Page<Room> rooms = roomMapper.findAll(specification, JpaUtil.getPageRequest(queryRoomStateReq.getPages()));
            // 根据查询结果和分页信息，构建并返回房间列表的响应对象
            RoomListRes listRes = PageResUtil.fillPagesDate(rooms, RoomListRes.class);
            if (roomStatus.contains(StatusTypeUtils.RoomStatus.OO)) {
                // 关联查询 Rmstat 日期列表和状态
                for (RoomRes record : listRes.getRecords()) {
                    record.setDescription(CustomData.getDesc(GlobalContext.getCurrentHotelId(), record.getRoomType(), SystemUtil.CustomDataKey.roomtype));
                    List<String> roomNos = listRes.getRecords().stream().map(RoomRes::getRoomNo).collect(Collectors.toList());
                    // 查询 Rmstat 信息
                    List<Rmstat> rmstatList = rmstatMapper.findRmstatByRoomNos(GlobalContext.getCurrentHotelId(), roomNos, null);
                    Map<String, List<Rmstat>> map = rmstatList.stream().collect(Collectors.groupingBy(Rmstat::getRoomNo));
                    if (map.get(record.getRoomNo()) != null && CollectionUtil.isNotEmpty(map.get(record.getRoomNo()))) {
                        List<Rmstat> rmstats = map.get(record.getRoomNo());
                        for (Rmstat rmstat : rmstats) {
                            RoomRes.RmstatInfo rmstatInfo = new RoomRes.RmstatInfo()
                                    .setStartTime(DateUtil.formatDate(rmstat.getFromDate()))
                                    .setEndTime(DateUtil.formatDate(rmstat.getToDate()))
                                    .setRoomStatus(rmstat.getStatus());
                            record.getRmstatInfos().add(rmstatInfo);
                        }
                    }
                }
            }
            return listRes;
        }
    }
}
