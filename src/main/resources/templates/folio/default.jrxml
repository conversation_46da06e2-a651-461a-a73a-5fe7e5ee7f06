<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="test" language="java" columnCount="1" pageWidth="420" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="380" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="15" uuid="09ae5b4a-6b7b-43f8-addc-60e253fccdce">
	<style name="NormalStyle" mode="Transparent"/>
	<style name="AlternateStyle" mode="Opaque" backcolor="#E6E6E6"/>
	<parameter name="resv" class="com.cw.entity.Reservation"/>
	<parameter name="hotelInfo" class="com.cw.pojo.entity.HotelInfoEntity"/>
	<parameter name="folio" class="com.cw.service.config.print.folio.FolioCalcFunc"/>
	<field name="acc" class="com.cw.entity.GuestAccounts"/>
	<pageHeader height="115">
		<element kind="textField" uuid="756f38cb-1bf4-486a-a118-3c1988fa6d0e" x="0" y="0" width="380" height="30" fontSize="17.0" blankWhenNull="true" bold="true" hTextAlign="Center" vTextAlign="Top">
			<expression><![CDATA[$P{hotelInfo}.getHotelName() + "客人账单"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="7ae290ae-222f-43c0-8342-50e781e25626" x="0" y="30" width="380" height="15" fontSize="9.0" blankWhenNull="true" bold="false" hTextAlign="Center" vTextAlign="Top">
			<expression><![CDATA["地址: " + $P{hotelInfo}.getAddress() + " 电话: " + $P{hotelInfo}.getTelephone()]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="355c85e6-162b-402b-a329-6f8124253209" x="0" y="45" width="65" height="17" fontSize="10.5" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["宾客姓名"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="9d15b9d1-af30-41e0-9fdc-693a3ccd171d" x="65" y="45" width="135" height="17" fontSize="10.5" textAdjust="ScaleFont" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getGuestName()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="2b5bff84-fd30-46a0-a21b-9fca6b08de91" x="200" y="45" width="60" height="17" fontSize="10.5" blankWhenNull="true" bold="false" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA["账单号"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="fcb1f8ad-02dd-40df-9d91-f82d7be24b3f" x="260" y="45" width="120" height="17" fontSize="10.5" textAdjust="ScaleFont" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getReservationNumber()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="f43aeadf-20e5-4137-97b9-78c13ea69bbe" x="0" y="62" width="65" height="17" fontSize="10.5" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["房　　号"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="0c1ce3b1-084d-4591-b896-d8cf504f9238" x="65" y="62" width="135" height="17" fontSize="10.5" textAdjust="ScaleFont" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getRoomNumber()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="c42d55c4-6610-40eb-be48-8d64220b49b6" x="200" y="62" width="60" height="17" fontSize="10.5" blankWhenNull="true" bold="false" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA["房　价"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="c3667708-7d8a-495d-abaa-004e1e61ef61" x="260" y="62" width="120" height="17" fontSize="10.5" textAdjust="ScaleFont" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getPrice()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="3e086c99-1a67-4f84-9dab-8e606125fcfa" x="0" y="79" width="65" height="17" fontSize="10.5" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["来店时间"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="8eca77f9-84a7-4326-a209-c7239154d2be" x="65" y="79" width="135" height="17" fontSize="10.5" textAdjust="ScaleFont" pattern="yyyy-MM-dd" linkType="None" linkTarget="Self" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getArrivalDate()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="ca923269-e706-4a20-80ac-d16adaeb9d88" x="200" y="79" width="60" height="17" fontSize="10.5" blankWhenNull="true" bold="false" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA["人　数"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="026fd5e5-f28a-4080-a955-84dff2d8e829" x="260" y="79" width="120" height="17" fontSize="10.5" textAdjust="ScaleFont" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getPersonTotal()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="999dc404-8460-4387-aa1c-ecc4e72e5631" x="0" y="96" width="65" height="17" fontSize="10.5" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["离店时间"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="d59948d1-b7d3-42fb-84f4-999ad8181f75" x="65" y="96" width="135" height="17" fontSize="10.5" textAdjust="ScaleFont" pattern="yyyy-MM-dd" linkTarget="Self" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getDepartureDate()]]></expression>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="e9295714-8d98-4825-a403-95c7c05b8988" x="200" y="96" width="60" height="17" fontSize="10.5" blankWhenNull="true" bold="false" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA["销售员"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="0b93ef87-ad08-4f8f-96f6-a9f991835521" x="260" y="96" width="120" height="17" fontSize="10.5" textAdjust="ScaleFont" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA[$P{resv}.getSalerid()]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageHeader>
	<columnHeader height="20">
		<element kind="textField" uuid="8215acf7-4fd6-476b-9ec2-07f6d98c9287" x="0" y="0" width="65" height="20" fontSize="10.5" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["日期"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="133cb524-4985-48b1-8a71-f9c47f511f31" x="65" y="0" width="195" height="20" fontSize="10.5" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
			<expression><![CDATA["项目"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="feec3543-f4e1-46d9-91a4-d23882a9b42f" x="260" y="0" width="60" height="20" fontSize="10.5" blankWhenNull="true" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA["消费"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="480d4907-481f-4d06-af1a-b6118acf0bd2" x="320" y="0" width="60" height="20" fontSize="10.5" blankWhenNull="true" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA["付款"]]></expression>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.5"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
	</columnHeader>
	<detail>
		<band height="17">
			<element kind="textField" uuid="15c166f9-bc80-4ab3-9492-c9fbf950cfb1" x="0" y="0" width="65" height="17" fontSize="10.5" textAdjust="ScaleFont" pattern="yyyy-MM-dd" linkType="None" linkTarget="Self" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{acc}.getBusiness_date()]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="20a9044e-204f-416f-97b5-40f1eb046dbc" x="65" y="0" width="195" height="17" fontSize="10.5" textAdjust="StretchHeight" linkType="None" linkTarget="Self" blankWhenNull="true" bold="false" hTextAlign="Left" vTextAlign="Middle">
				<expression><![CDATA[$F{acc}.getDescription() +" " + (null == $F{acc}.getRemark() ? "" : $F{acc}.getRemark())]]></expression>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="6702ed7e-a929-45bf-8340-8bb6f413de7b" x="260" y="0" width="60" height="17" fontSize="10.5" textAdjust="ScaleFont" linkType="None" linkTarget="Self" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA[null != $F{acc}.getPrice() && null != $F{acc}.getQuantity() && $F{acc}.getPrice().multiply($F{acc}.getQuantity()).compareTo(BigDecimal.ZERO)!=0?$F{acc}.getPrice().multiply($F{acc}.getQuantity()):""]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<element kind="textField" uuid="6d2a1c1e-c195-4c0a-8a84-f9c090be8022" x="320" y="0" width="60" height="17" fontSize="10.5" textAdjust="ScaleFont" linkType="None" linkTarget="Self" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
				<expression><![CDATA[null != $F{acc}.getCredit() && $F{acc}.getCredit().compareTo(BigDecimal.ZERO)!=0?$F{acc}.getCredit():""]]></expression>
				<box padding="0">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="15">
		<element kind="textField" uuid="06b7fa93-b8ac-49dc-82a1-c2bd10c5844e" x="0" y="0" width="380" height="15" fontSize="8.0" evaluationTime="Master" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Top">
			<expression><![CDATA[$V{MASTER_CURRENT_PAGE} + " / " + $V{MASTER_TOTAL_PAGES}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box topPadding="2" leftPadding="0" bottomPadding="0" rightPadding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="6c92b0d7-9566-4428-971d-433aff717418" x="0" y="0" width="380" height="15" fontSize="8.0">
			<expression><![CDATA[new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(new java.util.Date()) + " / " + $P{folio}.getOptUser()]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box topPadding="2">
				<topPen lineWidth="0.5"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
	<lastPageFooter height="35">
		<element kind="textField" uuid="299f2628-16e9-4a88-be50-e9ebd61c46fa" x="0" y="0" width="260" height="20" fontSize="11.0" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA["宾客签名"]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box topPadding="2" leftPadding="2" bottomPadding="2" rightPadding="2">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="9529fc20-750e-434a-940f-d62f5d3f71f8" x="0" y="20" width="380" height="15" fontSize="8.0" evaluationTime="Master" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Top">
			<expression><![CDATA[$V{MASTER_CURRENT_PAGE} + " / " + $V{MASTER_TOTAL_PAGES}]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box topPadding="2" leftPadding="0" bottomPadding="0" rightPadding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="edeea54f-a214-401d-88f4-54a1487f341a" x="0" y="20" width="380" height="15" fontSize="8.0">
			<expression><![CDATA[new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(new java.util.Date()) + " / " + $P{folio}.getOptUser()]]></expression>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box topPadding="2" leftPadding="0" bottomPadding="0" rightPadding="0">
				<topPen lineWidth="0.5"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</lastPageFooter>
	<summary height="35">
		<element kind="textField" uuid="660fd521-332b-4f13-a3d1-3c81a62f83e8" x="0" y="0" width="260" height="17" fontSize="10.0" blankWhenNull="true" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA["总额"]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="edde3179-4ee1-4fa1-84ca-8c81139770be" x="260" y="0" width="60" height="17" fontSize="11.0" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$P{folio}.getTotalDebit()]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="04ac93bb-c426-41d5-9961-964a167dbef3" x="320" y="0" width="60" height="17" fontSize="11.0" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$P{folio}.getTotalCredit()]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.0"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="37dcd269-5f22-4519-a72e-6aec8aeaa7f0" x="200" y="17" width="60" height="17" fontSize="10.0" blankWhenNull="true" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA["余额"]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<element kind="textField" uuid="3526d15e-0316-4da2-aede-5f5c34a43166" x="260" y="17" width="120" height="17" fontSize="11.0" blankWhenNull="true" bold="false" hTextAlign="Right" vTextAlign="Middle">
			<expression><![CDATA[$P{folio}.getTotalBalance()]]></expression>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<box padding="0">
				<topPen lineWidth="0.5"/>
				<leftPen lineWidth="0.0"/>
				<bottomPen lineWidth="0.0"/>
				<rightPen lineWidth="0.0"/>
			</box>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</summary>
</jasperReport>
