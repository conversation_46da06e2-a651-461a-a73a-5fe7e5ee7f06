package com.cw.testrun;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.PojoUtils;
import com.cw.arithmetic.SysFunLibTool;
import com.cw.arithmetic.sku.OpskuPickup;
import com.cw.arithmetic.sku.TMultiRsdata;
import com.cw.arithmetic.sku.TSkuUpd;
import com.cw.entity.Reservation;
import com.cw.pojo.dto.pms.req.reservation.UpdRsParamForm;
import com.cw.utils.CalculateDate;
import com.cw.utils.ProdType;
import com.cw.utils.SystemUtil;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2018/9/15 15:37
 **/
public class TestAvl {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    List<Reservation> oldrms = new ArrayList<>();

    List<Reservation> newrms = new ArrayList<>();

    String section = "001";


    @Before
    public void init() {


    }



    @Test
    public void 创建预订() {
        Date newd = CalculateDate.stringToDate("2018-10-01");
        Reservation rs = new Reservation();
        rs.setArrivalDate(newd);
        rs.setDepartureDate(CalculateDate.reckonDay(rs.getArrivalDate(), 5, 1));
        rs.setChannel("EW");
        rs.setRooms(1);
        rs.setRoomType("SZ22");
        newrms.add(rs);



        for (int i = 0; i < 1; i++) {
//            long s1=System.currentTimeMillis();
            TMultiRsdata olddata = new TMultiRsdata(true, "");
            TMultiRsdata newdata = new TMultiRsdata(false, "EW");
            newdata.fillArray(ProdType.ROOM.val(),Reservation.class,newrms);

            List<TSkuUpd> updList = OpskuPickup.calcSkuPickup(olddata, newdata);

            long s2 = System.currentTimeMillis();
//            System.out.println((s2-s1)+"ms ");

            printResult(updList);

        }


    }

    @Test
    public void 修改预订房型() {
        Date newd = CalculateDate.stringToDate("2018-10-01");
        Reservation rs = new Reservation();
        rs.setArrivalDate(newd);
        rs.setDepartureDate(CalculateDate.stringToDate("2018-10-02"));
        rs.setChannel("EW");
        rs.setRooms(3);
        rs.setRoomType("SZ22");
        oldrms.add(rs);

        Reservation updrs = PojoUtils.cloneEntity(rs);
        updrs.setArrivalDate(CalculateDate.stringToDate("2018-10-01"));
        updrs.setDepartureDate(CalculateDate.stringToDate("2018-10-02"));
        updrs.setRoomType("5");
        newrms.add(updrs);


        TMultiRsdata olddata = new TMultiRsdata(false, SystemUtil.DEF_RESOURCEID);
        olddata.fillArray(ProdType.ROOM.val(), Reservation.class, oldrms);
        TMultiRsdata newdata = new TMultiRsdata(false, SystemUtil.DEF_RESOURCEID);
        newdata.fillArray(ProdType.ROOM.val(), Reservation.class, newrms);

        List<TSkuUpd> updList = OpskuPickup.calcSkuPickup(olddata, newdata);

        printResult(updList);

    }

    @Test
    public void 测试延住() {
        Date newd = CalculateDate.stringToDate("2018-10-01");
        Reservation rs = new Reservation();
        rs.setArrivalDate(newd);
        rs.setDepartureDate(CalculateDate.stringToDate("2018-10-02"));
        rs.setRooms(3);
        rs.setRoomType("SZ22");
        oldrms.add(rs);

        Reservation updrs = PojoUtils.cloneEntity(rs);
        //updrs.setArrdate(CalculateDate.stringToDate("2018-10-01"));
        updrs.setDepartureDate(CalculateDate.stringToDate("2018-10-05"));
        newrms.add(updrs);


        TMultiRsdata olddata = new TMultiRsdata(false, SysFunLibTool.getAvlSource(""));
        olddata.fillArray(ProdType.ROOM.val(), Reservation.class, oldrms);
        TMultiRsdata newdata = new TMultiRsdata(false, SysFunLibTool.getAvlSource(""));
        newdata.fillArray(ProdType.ROOM.val(), Reservation.class, newrms);


        List<TSkuUpd> updList = OpskuPickup.calcSkuPickup(olddata, newdata);

        printResult(updList);

    }

    @Test
    public void 测试取消删除() {
        Date newd = CalculateDate.stringToDate("2018-10-01");
        Reservation rs = new Reservation();
        rs.setArrivalDate(newd);
        rs.setDepartureDate(CalculateDate.stringToDate("2018-10-02"));
//        rs.setBlock("EW");
        rs.setRooms(1);
        rs.setRoomType("SZ22");
        rs.setCancelDate(LocalDateTime.now());
//        rs.SETS(StatusUtil.RoomRsStatus.CANCELED);
        oldrms.add(rs);



        TMultiRsdata olddata = new TMultiRsdata(false, "EW");
        olddata.fillArray(ProdType.ROOM.val(), Reservation.class, oldrms);

        TMultiRsdata newdata = new TMultiRsdata(true, "");
        List<TSkuUpd> updList = OpskuPickup.calcSkuPickup(olddata, newdata);
        printResult(updList);
    }

    @Test
    public void tcopy() {
        String json = "{\"accompany\":\"[]\",\"arrivalDate\":1728921600000,\"balance\":-5089.00,\"block\":\"\",\"buildingNo\":\"7133635140\",\"channel\":\"2\",\n" +
                "\"consume\":798.00,\"createTime\":1729445945770,\"departureDate\":1732896000000,\"deposit\":0.00,\n" +
                "\"fixrate\":false,\"guestName\":\"十月十五15\",\"hotelId\":\"001\",\"modifiedTime\":1729445945770,\"naDate\":1729441804000,\n" +
                "\"payment\":-5887.00,\"personTotal\":1,\"price\":100.00,\"profileNumber\":\"7289579930\",\"rateCode\":\"SKJ\",\n" +
                "\"remark\":\"\",\"reservationNumber\":\"2024101512003\",\"reservationStatus\":1,\"reservationType\":\"QR\",\"roomNight\":1,\"roomNumber\":\"579\",\n" +
                "\"roomType\":\"GJTF\",\"rooms\":1,\"share\":0,\"telephone\":\"11111111112\",\"totalPrice\":5887.00}";
        Reservation rs = JSON.parseObject(json, Reservation.class);
        Reservation orgReservation = PojoUtils.cloneEntity(rs);
        //rs.setDepartureDate(CalculateDate.stringToDate("2024-12-01"));
        UpdRsParamForm form = new UpdRsParamForm();
        form.setDepartureDate(CalculateDate.stringToDate("2024-12-01"));
        BeanUtil.copyProperties(form, rs, CopyOptions.create().setIgnoreNullValue(true));

        TMultiRsdata olddata = new TMultiRsdata(false, SysFunLibTool.getAvlSource(orgReservation.getBlock()));
        olddata.fillArray(ProdType.ROOM.val(), Reservation.class, Arrays.asList(orgReservation));
        TMultiRsdata newdata = new TMultiRsdata(false, SysFunLibTool.getAvlSource(rs.getBlock()));
        newdata.fillArray(ProdType.ROOM.val(), Reservation.class, Arrays.asList(rs));
        List<TSkuUpd> updList = OpskuPickup.calcSkuPickup(olddata, newdata);

        printResult(updList);

    }




    private void printResult(List<TSkuUpd> updList) {
        List<TSkuUpd> rms = OpskuPickup.splitSkus(updList, ProdType.ROOM.val());

        for (TSkuUpd rm : rms) {
            logger.info("客房更新渠道{},产品{},占用数{},开始{},扣减结束{}", rm.getChannel(), rm.getSkuid(), (rm.getChangeNum() > 0 ? "增加" + rm.getChangeNum() : "减少" + rm.getChangeNum()),
                    CalculateDate.dateToString(rm.getStartdate()), CalculateDate.dateToString(rm.getEnddate()));
            System.out.println(OpskuPickup.getSkuMap(section, rm));
        }
        System.out.println("SKUKEY: " + OpskuPickup.getLockKeys(section, rms));



    }


}
