package com.cw.testrun;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.cw.arithmetic.rate.PriceTool;
import com.cw.pojo.common.PkgNode;
import com.cw.pojo.dto.pms.req.profile.ProfileReq;
import com.cw.pojo.dto.pms.req.reservation.RegistrationReq;
import com.cw.pojo.dto.pms.req.reservation.UpdateReservationReq;
import com.cw.service.mq.msgmodel.notifydata.ColOrderNotifyData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Descripstion
 * @Create 2024/10/11 10:58
 **/
@Slf4j
public class T3 {

    @Test
    public void recalc() {
        String pkgs = "";
        List<String> ls = Lists.newArrayList();
        ls.add("3|BRF,2|WC,1|TIC");
        ls.add(" 1|BRF,1|WC");
        List<PkgNode> pks = PriceTool.calculatePackages(ls, null);
        System.out.println(JSON.toJSONString(pks));
    }

    @Test
    public void xx() {
        UpdateReservationReq req = new UpdateReservationReq();
        System.out.println(req.getFixrate());

        System.out.println(RandomUtil.randomNumbers(6) + "");

        //PSBCUtil.postMsg(null);

    }

    @Test
    public void yy() {
        String a = "1000";
        String b = "9030";
        String c = "5000";
        String d = "5200";
        String x = "9000";
        List<String> ls = Arrays.asList(a, b, c, d, x);
        for (String l : ls) {
            log.info("{}****{}", l, l.compareTo(x));
        }
    }


}
